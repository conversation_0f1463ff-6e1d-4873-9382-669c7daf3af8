import pyautogui
import time
import cv2
import numpy as np
from PIL import Image
import random
from enum import Enum
from itertools import combinations
from collections import defaultdict


class GameState(Enum):
    PLAYING = "playing"
    WON = "won"
    LOST = "lost"
    UNKNOWN = "unknown"


class MinesweeperBot:
    def __init__(self):
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.001  # Minimal pause for maximum speed

        # Game board variables
        self.board_region = None
        self.cell_size = 25  # Will be recalculated
        self.board_width = 30  # Expert level
        self.board_height = 16  # Expert level
        self.board_top_left = None

        # Add border offsets to account for window borders
        self.border_offset_x = 0
        self.border_offset_y = 0

        # Game state tracking
        self.revealed_cells = set()
        self.flagged_cells = set()
        self.current_game_state = GameState.UNKNOWN

        # Performance optimization: screenshot caching
        self._cached_board_np = None
        self._cache_timestamp = 0
        self._cache_duration = 0.1  # Cache screenshots for 100ms

        # PERFORMANCE OPTIMIZATION: Track active numbered cells and cache calculations
        self.active_numbered_cells = set()  # Only cells with unrevealed neighbors
        self.neighbor_cache = {}  # Cache neighbor calculations
        self.last_board_state_hash = None  # Detect board changes for incremental updates

    def get_all_monitors_screenshot(self):
        """Get screenshot of all monitors combined"""
        try:
            from PIL import ImageGrab
            screenshot = ImageGrab.grab(bbox=None, all_screens=True)
            return screenshot
        except Exception as e:
            return pyautogui.screenshot()

    def sample_colors_from_image(self, image_np, x, y, w, h, sample_size=50):
        """Sample colors from a specific region to understand the actual colors present"""
        print(f"\n🎨 Sampling colors from region ({x}, {y}, {w}, {h})")

        # Extract the region
        region = image_np[y:y + h, x:x + w]

        # Convert to HSV
        hsv_region = cv2.cvtColor(region, cv2.COLOR_RGB2HSV)

        # Sample random pixels
        height, width = region.shape[:2]
        sample_points = min(sample_size, width * height // 10)

        colors_rgb = []
        colors_hsv = []

        for _ in range(sample_points):
            rand_y = random.randint(0, height - 1)
            rand_x = random.randint(0, width - 1)

            rgb_color = region[rand_y, rand_x]
            hsv_color = hsv_region[rand_y, rand_x]

            colors_rgb.append(rgb_color)
            colors_hsv.append(hsv_color)

        # Analyze the colors
        print(f"Sampled {len(colors_hsv)} pixels")

        # Find blue-ish colors
        blue_colors = []
        for i, (rgb, hsv) in enumerate(zip(colors_rgb, colors_hsv)):
            h, s, v = hsv
            r, g, b = rgb

            # Look for colors that might be "blue-ish"
            if b > r and b > g and s > 30 and v > 50:  # Basic blue detection
                blue_colors.append({
                    'rgb': (int(r), int(g), int(b)),
                    'hsv': (int(h), int(s), int(v))
                })

        print(f"Found {len(blue_colors)} potentially blue pixels:")
        for i, color in enumerate(blue_colors[:10]):  # Show first 10
            rgb = color['rgb']
            hsv = color['hsv']
            print(f"  Blue #{i + 1}: RGB{rgb} -> HSV{hsv}")

        return blue_colors

    def detect_minesweeper_with_color_analysis(self, image_np):
        """Enhanced detection using color analysis - improved for multi-monitor"""
        print("🔍 Detecting Minesweeper board...")

        # First, let's look for blue regions (typical minesweeper unrevealed cells)
        hsv = cv2.cvtColor(image_np, cv2.COLOR_RGB2HSV)

        # Multiple blue ranges to catch different minesweeper themes
        blue_ranges = [
            ([100, 50, 100], [130, 255, 255], "Standard blue"),
            ([90, 30, 80], [140, 255, 255], "Light blue"),
            ([80, 40, 120], [120, 255, 255], "Dark blue"),
            ([105, 80, 150], [125, 255, 255], "Saturated blue"),
        ]

        all_candidates = []

        for i, (lower, upper, desc) in enumerate(blue_ranges):
            lower_blue = np.array(lower)
            upper_blue = np.array(upper)
            blue_mask = cv2.inRange(hsv, lower_blue, upper_blue)

            # Count blue pixels
            blue_pixels = np.sum(blue_mask > 0)
            total_pixels = blue_mask.shape[0] * blue_mask.shape[1]
            blue_percentage = (blue_pixels / total_pixels) * 100

            if blue_percentage > 0.5:  # Lowered threshold for larger windows
                # Find contours in this blue mask
                contours, _ = cv2.findContours(blue_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                for contour in contours:
                    x, y, w, h = cv2.boundingRect(contour)
                    area = w * h

                    # Look for large rectangular blue regions - increased limits for larger windows
                    if area > 15000 and w > 250 and h > 120:  # More flexible size limits
                        aspect_ratio = w / h

                        # Expert minesweeper is roughly 30:16 = 1.875, but be flexible
                        if 1.0 < aspect_ratio < 4.0:
                            candidate = {
                                'bounds': (x, y, w, h),
                                'area': area,
                                'aspect_ratio': aspect_ratio,
                                'blue_range': desc,
                                'blue_percentage': blue_percentage
                            }
                            all_candidates.append(candidate)

        # Sort candidates by area (largest first)
        all_candidates.sort(key=lambda c: c['area'], reverse=True)

        if len(all_candidates) == 0:
            print("❌ No candidates found! This might be why detection is failing.")
            print("🔧 Try making sure Minesweeper window is fully visible and has unrevealed blue cells.")
            return False

        # Test each candidate
        for i, candidate in enumerate(all_candidates[:5]):  # Test top 5 candidates
            x, y, w, h = candidate['bounds']

            # Extract the candidate region
            region = image_np[y:y+h, x:x+w]

            # Test if this looks like a minesweeper board
            if self.validate_minesweeper_region(region, candidate):

                # Extract just the cell grid from the detected region
                cell_grid_region = self.extract_cell_grid_from_region(image_np, x, y, w, h)

                if cell_grid_region:
                    cell_x, cell_y, cell_w, cell_h = cell_grid_region

                    # Set board properties to point to just the cell grid
                    self.board_top_left = pyautogui.Point(cell_x, cell_y)
                    self.board_region = (cell_x, cell_y, cell_w, cell_h)

                    # Calculate cell dimensions
                    self.cell_width = cell_w / self.board_width
                    self.cell_height = cell_h / self.board_height
                    self.cell_size = int((self.cell_width + self.cell_height) / 2)

                    print(f"✅ Minesweeper board detected! Cell size: {self.cell_width:.1f} x {self.cell_height:.1f}")

                    return True

        return False

    def extract_cell_grid_from_region(self, image_np, x, y, w, h):
        """Extract just the cell grid area using black grid lines"""
        try:
            # Extract the detected region
            region = image_np[y:y+h, x:x+w]

            # Convert to grayscale for line detection
            gray_region = cv2.cvtColor(region, cv2.COLOR_RGB2GRAY)

            # Detect dark lines (grid lines between cells)
            # Black/dark gray lines are typically < 100 in grayscale
            dark_mask = gray_region < 80

            # Find horizontal and vertical lines using morphological operations
            # Horizontal lines
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (15, 1))
            horizontal_lines = cv2.morphologyEx(dark_mask.astype(np.uint8), cv2.MORPH_OPEN, horizontal_kernel)

            # Vertical lines
            vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 15))
            vertical_lines = cv2.morphologyEx(dark_mask.astype(np.uint8), cv2.MORPH_OPEN, vertical_kernel)

            # Combine horizontal and vertical lines
            grid_lines = horizontal_lines + vertical_lines

            # Find the bounding box of the grid
            grid_coords = np.where(grid_lines > 0)

            if len(grid_coords[0]) > 0:
                # Get the bounding box of grid lines
                min_y, max_y = np.min(grid_coords[0]), np.max(grid_coords[0])
                min_x, max_x = np.min(grid_coords[1]), np.max(grid_coords[1])

                # Try to detect and exclude the bottom score area
                adjusted_max_y = self.detect_and_exclude_score_area(region, grid_lines, min_y, max_y)
                if adjusted_max_y < max_y:
                    max_y = adjusted_max_y

                # The actual cell area should be slightly inside the grid lines
                # Add small margin to get to cell centers, not grid lines
                cell_margin = 2
                cell_min_x = min_x + cell_margin
                cell_min_y = min_y + cell_margin
                cell_max_x = max_x - cell_margin
                cell_max_y = max_y - cell_margin

                # Calculate cell grid dimensions
                cell_grid_w = cell_max_x - cell_min_x
                cell_grid_h = cell_max_y - cell_min_y

                # Convert back to absolute coordinates
                cell_grid_x = x + cell_min_x
                cell_grid_y = y + cell_min_y

                # Validate the cell grid dimensions
                expected_cell_w = cell_grid_w / self.board_width
                expected_cell_h = cell_grid_h / self.board_height

                if 8 < expected_cell_w < 100 and 8 < expected_cell_h < 100:  # Increased limits for larger windows
                    return (cell_grid_x, cell_grid_y, cell_grid_w, cell_grid_h)
            else:
                print("❌ No grid lines found, trying edge detection fallback...")
                # Fallback: try edge detection approach
                return self.extract_cell_grid_using_edges(region, x, y, w, h)

        except Exception as e:
            pass

        return None

    def detect_and_exclude_score_area(self, region, grid_lines, min_y, max_y):
        """Detect and exclude the bottom score area from the cell grid"""
        try:
            h, w = region.shape[:2]

            # Method 1: Use expected dimensions - Expert Minesweeper is exactly 16 rows
            # Calculate what the cell height should be
            total_grid_height = max_y - min_y
            expected_cell_height = total_grid_height / self.board_height

            # The actual cell grid should be exactly 16 * cell_height
            expected_grid_height = expected_cell_height * self.board_height
            expected_max_y = int(min_y + expected_grid_height)

            # If there's extra height beyond what 16 rows should take, it's likely score area
            if max_y > expected_max_y + 5:  # 5 pixel tolerance
                return expected_max_y

            # Method 2: Look for the last horizontal grid line that spans most of the width
            # Work backwards from max_y to find the last good horizontal line
            for y in range(max_y - 1, min_y, -1):
                # Check how much of this horizontal line has grid pixels
                line_pixels = np.sum(grid_lines[y, :] > 0)
                line_coverage = line_pixels / w

                # If this line spans a good portion of the width, it's likely a cell boundary
                if line_coverage > 0.6:  # Line covers >60% of width
                    # Add small margin to ensure we're inside the cells
                    adjusted_max_y = y - 2
                    if adjusted_max_y < max_y - 10:  # Only adjust if we're removing significant area
                        return adjusted_max_y
                    break

            # Method 3: Analyze vertical sections for grid density changes
            section_height = max(10, (max_y - min_y) // 8)  # Smaller sections for better detection

            for section_start in range(max_y - section_height, min_y, -section_height):
                section_end = min(max_y, section_start + section_height)

                # Count grid pixels in this section
                section_grid = grid_lines[section_start:section_end, :]
                section_pixels = np.sum(section_grid > 0)

                # Compare with section above
                if section_start - section_height >= min_y:
                    above_start = section_start - section_height
                    above_end = section_start
                    above_grid = grid_lines[above_start:above_end, :]
                    above_pixels = np.sum(above_grid > 0)

                    # If current section has much fewer grid pixels, it's likely score area
                    if above_pixels > 0 and section_pixels < above_pixels * 0.4:
                        return section_start

            return max_y

        except Exception as e:
            return max_y

    def extract_cell_grid_using_edges(self, region, x, y, w, h):
        """Fallback method using edge detection"""
        try:
            print(f"🔍 Edge detection fallback for region {w}x{h}")
            gray_region = cv2.cvtColor(region, cv2.COLOR_RGB2GRAY)

            # Use Canny edge detection
            edges = cv2.Canny(gray_region, 50, 150)

            # Find contours in edges
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            print(f"🔍 Found {len(contours)} contours in edge detection")

            # Look for rectangular contours that might be the cell grid
            for contour in contours:
                contour_x, contour_y, contour_w, contour_h = cv2.boundingRect(contour)
                area = contour_w * contour_h

                # Look for large rectangular areas
                if area > (w * h * 0.3):  # At least 30% of the region
                    expected_cell_w = contour_w / self.board_width
                    expected_cell_h = contour_h / self.board_height

                    if 8 < expected_cell_w < 80 and 8 < expected_cell_h < 80:  # Increased limits for larger windows
                        cell_grid_x = x + contour_x
                        cell_grid_y = y + contour_y

                        return (cell_grid_x, cell_grid_y, contour_w, contour_h)

        except Exception as e:
            pass

        return None

    def validate_minesweeper_region(self, region, candidate):
        """Validate if a region looks like a minesweeper board"""
        h, w = region.shape[:2]

        # Check 1: Size should be reasonable for expert minesweeper
        expected_cell_width = w / 30  # Expert is 30 wide
        expected_cell_height = h / 16  # Expert is 16 tall

        if expected_cell_width < 8 or expected_cell_width > 100:  # Increased upper limit for larger windows
            return False

        if expected_cell_height < 8 or expected_cell_height > 100:  # Increased upper limit for larger windows
            return False

        # Check 2: Should have a good amount of blue (unrevealed cells)
        hsv_region = cv2.cvtColor(region, cv2.COLOR_RGB2HSV)

        # Use a broad blue range
        lower_blue = np.array([80, 30, 80])
        upper_blue = np.array([140, 255, 255])
        blue_mask = cv2.inRange(hsv_region, lower_blue, upper_blue)

        blue_pixels = np.sum(blue_mask > 0)
        total_pixels = blue_mask.shape[0] * blue_mask.shape[1]
        blue_percentage = (blue_pixels / total_pixels) * 100

        if blue_percentage < 15:  # Lowered threshold for larger windows
            return False

        # Check 3: Should have some grid-like structure
        gray_region = cv2.cvtColor(region, cv2.COLOR_RGB2GRAY)

        # Look for horizontal and vertical lines (grid structure)
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 1))
        vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 25))

        horizontal_lines = cv2.morphologyEx(gray_region, cv2.MORPH_OPEN, horizontal_kernel)
        vertical_lines = cv2.morphologyEx(gray_region, cv2.MORPH_OPEN, vertical_kernel)

        horizontal_score = np.sum(horizontal_lines > 0)
        vertical_score = np.sum(vertical_lines > 0)

        # If we have some grid structure, that's good
        grid_bonus = horizontal_score > 100 and vertical_score > 100

        # Check 4: Color variety (should have different colors for numbers, etc.)
        unique_colors = len(np.unique(region.reshape(-1, region.shape[2]), axis=0))

        if unique_colors < 3:  # Lowered threshold
            return False

        # Final decision
        score = 0
        if blue_percentage >= 25:
            score += 2
        elif blue_percentage >= 15:
            score += 1

        if grid_bonus:
            score += 1

        if unique_colors >= 8:
            score += 1
        elif unique_colors >= 3:
            score += 0.5

        return score >= 2.0  # Lowered threshold

    def test_hsv_range(self, image_np, lower_hsv, upper_hsv, candidate):
        """Test a specific HSV range and see if it detects the board well"""
        print(f"🧪 Testing HSV range: {lower_hsv} to {upper_hsv}")

        hsv = cv2.cvtColor(image_np, cv2.COLOR_RGB2HSV)

        lower_bound = np.array(lower_hsv)
        upper_bound = np.array(upper_hsv)

        mask = cv2.inRange(hsv, lower_bound, upper_bound)

        # Save this test mask
        cv2.imwrite("test_blue_mask.png", mask)
        print("💾 Saved test mask as 'test_blue_mask.png'")

        # Check if this mask captures a good portion of the candidate region
        x, y, w, h = candidate['bounds']
        region_mask = mask[y:y + h, x:x + w]

        # Count blue pixels in the region
        blue_pixels = np.sum(region_mask > 0)
        total_pixels = w * h
        blue_percentage = (blue_pixels / total_pixels) * 100

        print(f"📊 Blue pixel percentage in region: {blue_percentage:.1f}%")

        # If we have a decent amount of blue pixels, this is probably our board
        if blue_percentage > 30:  # At least 30% should be blue for a fresh minesweeper board
            print("✅ Good blue coverage - using this region!")

            # Set board properties
            self.board_top_left = pyautogui.Point(x, y)
            self.board_region = (x, y, w, h)

            # Calculate cell dimensions
            self.cell_width = w / self.board_width
            self.cell_height = h / self.board_height
            self.cell_size = int((self.cell_width + self.cell_height) / 2)

            print(f"📏 Cell dimensions: {self.cell_width:.2f} x {self.cell_height:.2f}")
            print(f"📏 Average cell size: {self.cell_size}")

            return True
        else:
            print(f"❌ Only {blue_percentage:.1f}% blue pixels - not enough")

        return False

    def get_monitor_info(self):
        """Get detailed information about monitor setup"""
        try:
            import tkinter as tk
            root = tk.Tk()

            # Get screen dimensions
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()

            root.destroy()

            print(f"🖥️ Primary monitor: {screen_width}x{screen_height}")

            # Get multi-monitor screenshot to see total dimensions
            screenshot = self.get_all_monitors_screenshot()
            total_width, total_height = screenshot.size

            print(f"📺 Total desktop screenshot: {total_width}x{total_height}")

            # Test mouse coordinate system
            print(f"\n🖱️ Testing mouse coordinate system...")
            current_pos = pyautogui.position()
            print(f"Current mouse position: {current_pos}")

            # Get screen size as pyautogui sees it
            pyautogui_size = pyautogui.size()
            print(f"PyAutoGUI screen size: {pyautogui_size}")

            # Calculate if we have multiple monitors
            if total_width > screen_width or total_width != pyautogui_size.width:
                print(f"🖥️ Multi-monitor detected!")
                print(f"Screenshot width: {total_width}")
                print(f"PyAutoGUI width: {pyautogui_size.width}")
                print(f"Primary monitor width: {screen_width}")

                return {
                    'primary_width': screen_width,
                    'primary_height': screen_height,
                    'total_width': total_width,
                    'total_height': total_height,
                    'pyautogui_width': pyautogui_size.width,
                    'pyautogui_height': pyautogui_size.height,
                    'has_multiple': True
                }
            else:
                return {
                    'primary_width': screen_width,
                    'primary_height': screen_height,
                    'total_width': total_width,
                    'total_height': total_height,
                    'pyautogui_width': pyautogui_size.width,
                    'pyautogui_height': pyautogui_size.height,
                    'has_multiple': False
                }
        except Exception as e:
            print(f"❌ Error getting monitor info: {e}")
            return None

    def debug_coordinate_system(self):
        """Debug the coordinate system to understand monitor layout"""
        print("\n🔍 DEBUGGING COORDINATE SYSTEM")
        print("=" * 50)

        monitor_info = self.get_monitor_info()

        print("\n🧪 Let's test the coordinate system...")
        print("I'll ask you to move your mouse to different positions")
        print("This will help us understand how your monitors are arranged")

        positions_to_test = [
            "TOP-LEFT corner of your PRIMARY monitor",
            "TOP-LEFT corner of your SECOND monitor (where Minesweeper is)",
            "TOP-LEFT corner of your THIRD monitor",
            "TOP-LEFT corner of the Minesweeper game board"
        ]

        recorded_positions = []

        for i, position_desc in enumerate(positions_to_test):
            print(f"\n📍 Test {i+1}: Move mouse to {position_desc}")
            input("Press Enter when positioned...")

            pos = pyautogui.position()
            print(f"✅ Recorded: {pos}")
            recorded_positions.append((position_desc, pos))

        print(f"\n📊 COORDINATE ANALYSIS:")
        for desc, pos in recorded_positions:
            print(f"  {desc}: {pos}")

        # Analyze the pattern
        if len(recorded_positions) >= 4:
            primary_pos = recorded_positions[0][1]
            second_pos = recorded_positions[1][1]
            third_pos = recorded_positions[2][1]
            game_pos = recorded_positions[3][1]

            print(f"\n🔍 ANALYSIS:")
            print(f"Primary monitor starts at: {primary_pos}")
            print(f"Second monitor starts at: {second_pos}")
            print(f"Third monitor starts at: {third_pos}")
            print(f"Game board starts at: {game_pos}")

            # Calculate monitor arrangement
            if second_pos.x > primary_pos.x:
                print("📐 Second monitor is to the RIGHT of primary")
                second_offset_x = second_pos.x - primary_pos.x
            else:
                print("📐 Second monitor is to the LEFT of primary")
                second_offset_x = primary_pos.x - second_pos.x

            print(f"📏 Second monitor X offset: {second_offset_x}")

            return {
                'positions': recorded_positions,
                'monitor_info': monitor_info,
                'second_offset_x': second_offset_x,
                'game_position': game_pos
            }

        return None

    def manual_board_setup(self):
        """Manual setup - let user click corners to define board"""
        print("\n🎯 MANUAL BOARD SETUP")
        print("We'll manually define the game board by clicking corners")
        print("This will ensure perfect accuracy!")

        # Get monitor information first
        monitor_info = self.get_monitor_info()
        if monitor_info and monitor_info['has_multiple']:
            print(f"\n🖥️ Multi-monitor setup detected!")
            print(f"Primary monitor: {monitor_info['primary_width']}x{monitor_info['primary_height']}")
            print(f"Total desktop: {monitor_info['total_width']}x{monitor_info['total_height']}")
            print("📍 Make sure to note which monitor the game is on!")

        input("Press Enter when ready to start manual setup...")

        # Get top-left corner
        print("\n📍 STEP 1: Click the TOP-LEFT corner of the first cell")
        print("Move your mouse to the TOP-LEFT corner of the very first minesweeper cell")
        print("Then press Enter...")
        input()

        top_left = pyautogui.position()
        print(f"✅ Top-left recorded: {top_left}")

        # Get bottom-right corner
        print("\n📍 STEP 2: Click the BOTTOM-RIGHT corner of the last cell")
        print("Move your mouse to the BOTTOM-RIGHT corner of the very last cell (bottom-right of grid)")
        print("Then press Enter...")
        input()

        bottom_right = pyautogui.position()
        print(f"✅ Bottom-right recorded: {bottom_right}")

        # Calculate board dimensions
        board_width_pixels = bottom_right.x - top_left.x
        board_height_pixels = bottom_right.y - top_left.y

        # Calculate cell size
        cell_width = board_width_pixels / self.board_width
        cell_height = board_height_pixels / self.board_height

        print(f"\n📏 CALCULATIONS:")
        print(f"Board size in pixels: {board_width_pixels} x {board_height_pixels}")
        print(f"Cell width: {cell_width:.2f} pixels")
        print(f"Cell height: {cell_height:.2f} pixels")

        # Store the values
        self.board_top_left = top_left
        self.board_region = (top_left.x, top_left.y, board_width_pixels, board_height_pixels)
        self.cell_size = int((cell_width + cell_height) / 2)

        # More precise cell dimensions
        self.cell_width = cell_width
        self.cell_height = cell_height

        print(f"✅ Setup complete!")

        # Test the coordinates immediately
        test_screenshot = self.get_all_monitors_screenshot()

        # Try a small test crop to see if coordinates work
        test_x, test_y = top_left.x, top_left.y
        test_w, test_h = min(100, board_width_pixels), min(100, board_height_pixels)

        test_crop = test_screenshot.crop((test_x, test_y, test_x + test_w, test_y + test_h))

        # Check if the test crop is reasonable
        test_np = np.array(test_crop)
        test_brightness = np.mean(test_np)

        if test_brightness < 10:
            print("⚠️ WARNING: Test crop is very dark - coordinates may be wrong!")

            # Try to fix multi-monitor coordinates
            if monitor_info and monitor_info['has_multiple']:
                fixed = self.fix_multimonitor_coordinates(monitor_info, top_left, bottom_right)
                if fixed:
                    print("✅ Multi-monitor coordinates fixed!")
                    return True
                else:
                    print("❌ Could not fix coordinates automatically")

            return False
        else:
            print("✅ Coordinates look good!")

        return True

    def fix_multimonitor_coordinates(self, monitor_info, top_left, bottom_right):
        """Fix coordinates for your specific 3-monitor setup"""
        print("\n🔧 FIXING MULTI-MONITOR COORDINATES FOR YOUR SETUP")

        # Based on your debug output:
        # Primary: (-2304, 1265), Second: (0, 0), Third: (3072, 0)
        # Game is on second monitor at (729, 408)

        print("🖥️ Detected your specific monitor layout:")
        print("  Primary: (-2304, 1265) - Left and below")
        print("  Second: (0, 0) - Center, where Minesweeper is")
        print("  Third: (3072, 0) - Right")

        # For your setup, we need to understand how the screenshot is arranged
        screenshot = self.get_all_monitors_screenshot()
        total_width, total_height = screenshot.size
        print(f"📺 Screenshot dimensions: {total_width} x {total_height}")

        # Your mouse coordinates are already correct for the second monitor
        # The issue is likely how the screenshot arranges the monitors

        # Let's try different coordinate mappings
        print("\n🧪 Testing coordinate mappings...")

        # Option 1: Direct coordinates (mouse coords = screenshot coords)
        print("🔍 Testing direct coordinate mapping...")
        test_crop1 = screenshot.crop((top_left.x, top_left.y, top_left.x + 100, top_left.y + 100))
        test_crop1.save("test_direct_coords.png")
        brightness1 = np.mean(np.array(test_crop1))
        print(f"💡 Direct mapping brightness: {brightness1:.2f}")

        # Option 2: Offset by primary monitor position (add 2304 to x)
        print("🔍 Testing offset coordinate mapping...")
        offset_x = top_left.x + 2304  # Add primary monitor width
        offset_y = top_left.y
        if offset_x >= 0 and offset_y >= 0 and offset_x < total_width and offset_y < total_height:
            test_crop2 = screenshot.crop((offset_x, offset_y, offset_x + 100, offset_y + 100))
            test_crop2.save("test_offset_coords.png")
            brightness2 = np.mean(np.array(test_crop2))
            print(f"💡 Offset mapping brightness: {brightness2:.2f}")
        else:
            brightness2 = 0
            print("💡 Offset mapping out of bounds")

        # Option 3: Try different arrangement (monitors might be arranged differently in screenshot)
        print("🔍 Testing alternative arrangement...")
        # Maybe the screenshot arranges monitors as: [Primary][Second][Third]
        alt_x = top_left.x + 2304  # Assuming primary is 2304 wide
        alt_y = top_left.y - 1265  # Adjust for vertical offset
        if alt_x >= 0 and alt_y >= 0 and alt_x < total_width and alt_y < total_height:
            test_crop3 = screenshot.crop((alt_x, alt_y, alt_x + 100, alt_y + 100))
            test_crop3.save("test_alt_coords.png")
            brightness3 = np.mean(np.array(test_crop3))
            print(f"💡 Alternative mapping brightness: {brightness3:.2f}")
        else:
            brightness3 = 0
            print("💡 Alternative mapping out of bounds")

        # Choose the best mapping
        best_brightness = max(brightness1, brightness2, brightness3)

        if best_brightness > 10:
            if brightness1 == best_brightness:
                print("✅ Direct coordinate mapping works!")
                # No adjustment needed
                return True
            elif brightness2 == best_brightness:
                print("✅ Offset coordinate mapping works!")
                # Adjust coordinates by adding primary monitor width
                adjusted_top_left_x = top_left.x + 2304
                adjusted_top_left_y = top_left.y
                adjusted_bottom_right_x = bottom_right.x + 2304
                adjusted_bottom_right_y = bottom_right.y

                # Update stored coordinates
                self.board_top_left = pyautogui.Point(adjusted_top_left_x, adjusted_top_left_y)
                board_width_pixels = adjusted_bottom_right_x - adjusted_top_left_x
                board_height_pixels = adjusted_bottom_right_y - adjusted_top_left_y
                self.board_region = (adjusted_top_left_x, adjusted_top_left_y, board_width_pixels, board_height_pixels)

                print(f"✅ Updated board region: {self.board_region}")
                return True
            elif brightness3 == best_brightness:
                print("✅ Alternative coordinate mapping works!")
                # Adjust coordinates for alternative arrangement
                adjusted_top_left_x = top_left.x + 2304
                adjusted_top_left_y = top_left.y - 1265
                adjusted_bottom_right_x = bottom_right.x + 2304
                adjusted_bottom_right_y = bottom_right.y - 1265

                # Update stored coordinates
                self.board_top_left = pyautogui.Point(adjusted_top_left_x, adjusted_top_left_y)
                board_width_pixels = adjusted_bottom_right_x - adjusted_top_left_x
                board_height_pixels = adjusted_bottom_right_y - adjusted_top_left_y
                self.board_region = (adjusted_top_left_x, adjusted_top_left_y, board_width_pixels, board_height_pixels)

                print(f"✅ Updated board region: {self.board_region}")
                return True

        print("❌ None of the coordinate mappings worked")
        return False

    def convert_screenshot_to_mouse_coords(self, screenshot_x, screenshot_y):
        """Convert screenshot coordinates to mouse coordinates for your 3-monitor setup"""

        # Use calibrated offset if available
        if hasattr(self, 'mouse_offset_x') and hasattr(self, 'mouse_offset_y'):
            mouse_x = screenshot_x + self.mouse_offset_x
            mouse_y = screenshot_y + self.mouse_offset_y
            print(f"🔄 Using calibrated offset: screenshot ({screenshot_x}, {screenshot_y}) + offset ({self.mouse_offset_x}, {self.mouse_offset_y}) = mouse ({mouse_x}, {mouse_y})")
        else:
            # Fallback to estimated conversion based on your monitor layout
            # Primary: (-2304, 1265), Second: (0, 0), Third: (3072, 0)
            mouse_x = screenshot_x - 2304  # Subtract primary monitor width
            mouse_y = screenshot_y - 1265  # Subtract primary monitor Y offset
            print(f"🔄 Using estimated conversion: screenshot ({screenshot_x}, {screenshot_y}) to mouse ({mouse_x}, {mouse_y})")

        return (mouse_x, mouse_y)

    def simple_mouse_calibration(self):
        """Simple and reliable mouse calibration using direct clicks"""
        print("\n🎯 SIMPLE MOUSE CALIBRATION")
        print("=" * 50)
        print("This method uses direct mouse coordinates - no complex conversions!")

        # Ask user to click top-left cell
        print("\n📍 STEP 1: Click the TOP-LEFT cell of the Minesweeper grid")
        print("(The very first clickable cell)")
        input("Position your mouse over the top-left cell and press Enter...")

        # Get current mouse position
        top_left_mouse = pyautogui.position()
        print(f"✅ Top-left cell position: {top_left_mouse}")

        # Ask user to click bottom-right cell
        print("\n📍 STEP 2: Click the BOTTOM-RIGHT cell of the Minesweeper grid")
        print("(The very last clickable cell)")
        input("Position your mouse over the bottom-right cell and press Enter...")

        # Get current mouse position
        bottom_right_mouse = pyautogui.position()
        print(f"✅ Bottom-right cell position: {bottom_right_mouse}")

        # Calculate cell dimensions from actual positions
        # These are the centers of the corner cells
        grid_width_pixels = bottom_right_mouse.x - top_left_mouse.x
        grid_height_pixels = bottom_right_mouse.y - top_left_mouse.y

        # Expert minesweeper is 30x16, so we have 29 gaps between centers
        cell_width = grid_width_pixels / (self.board_width - 1)
        cell_height = grid_height_pixels / (self.board_height - 1)

        # Store the calibration using direct mouse coordinates
        self.board_top_left = top_left_mouse
        self.cell_width = cell_width
        self.cell_height = cell_height

        # Mark that we're using direct mouse coordinates
        self.use_direct_mouse_coords = True

        print(f"✅ Calibration complete!")
        print(f"📏 Cell size: {cell_width:.1f} x {cell_height:.1f}")

        return True

    def get_cell_position_direct(self, row, col):
        """Get cell position using direct mouse coordinates (no conversion needed)"""
        if not self.board_top_left:
            return None

        if not hasattr(self, 'cell_width') or not hasattr(self, 'cell_height'):
            print("❌ Cell dimensions not calibrated!")
            return None

        # Calculate position directly in mouse coordinates
        # board_top_left is already the center of cell (0,0)
        mouse_x = self.board_top_left.x + (col * self.cell_width)
        mouse_y = self.board_top_left.y + (row * self.cell_height)

        return (int(mouse_x), int(mouse_y))

    def get_cell_position_precise(self, row, col):
        """Get precise cell position - uses direct coordinates if available"""
        if not self.board_top_left:
            return None

        # If we have direct mouse calibration, use it
        if hasattr(self, 'use_direct_mouse_coords') and self.use_direct_mouse_coords:
            return self.get_cell_position_direct(row, col)

        # Otherwise use the old method with coordinate conversion
        if hasattr(self, 'cell_width') and hasattr(self, 'cell_height'):
            # Calculate center of the cell in screenshot coordinates
            screenshot_x = self.board_top_left.x + (col * self.cell_width) + (self.cell_width / 2)
            screenshot_y = self.board_top_left.y + (row * self.cell_height) + (self.cell_height / 2)
        else:
            # Fallback to square cells
            screenshot_x = self.board_top_left.x + (col * self.cell_size) + (self.cell_size // 2)
            screenshot_y = self.board_top_left.y + (row * self.cell_size) + (self.cell_size // 2)

        # Convert screenshot coordinates to mouse coordinates
        mouse_x, mouse_y = self.convert_screenshot_to_mouse_coords(int(screenshot_x), int(screenshot_y))

        return (mouse_x, mouse_y)



    def try_expanded_hsv_ranges(self, image_np):
        """Try many different HSV ranges to find the right blue"""
        hsv = cv2.cvtColor(image_np, cv2.COLOR_RGB2HSV)

        # Much more comprehensive HSV ranges for blue detection
        blue_ranges = [
            # Original ranges
            ([100, 50, 150], [130, 255, 255], "Standard blue"),
            ([90, 30, 100], [140, 255, 255], "Wider blue range"),
            ([80, 20, 80], [150, 255, 255], "Very wide blue range"),

            # New ranges based on typical minesweeper colors
            ([100, 100, 100], [130, 255, 255], "Saturated blue"),
            ([105, 80, 120], [125, 255, 255], "Medium blue"),
            ([95, 60, 140], [135, 255, 255], "Bright blue"),
            ([85, 40, 90], [145, 255, 255], "Extended blue"),
            ([110, 120, 160], [120, 255, 255], "Narrow bright blue"),

            # Try some edge cases
            ([75, 10, 50], [155, 255, 255], "Maximum range"),
            ([100, 200, 200], [120, 255, 255], "High saturation"),
        ]

        for i, (lower, upper, description) in enumerate(blue_ranges):
            print(f"\n🔍 Trying {description}: HSV {lower} to {upper}")

            lower_blue = np.array(lower)
            upper_blue = np.array(upper)
            blue_mask = cv2.inRange(hsv, lower_blue, upper_blue)

            # Save the mask
            cv2.imwrite(f"expanded_blue_mask_{i}.png", blue_mask)
            print(f"💾 Saved blue mask as 'expanded_blue_mask_{i}.png'")

            # Count total blue pixels
            blue_pixels = np.sum(blue_mask > 0)
            total_pixels = blue_mask.shape[0] * blue_mask.shape[1]
            blue_percentage = (blue_pixels / total_pixels) * 100

            print(f"📊 Total blue pixels: {blue_pixels} ({blue_percentage:.2f}% of screen)")

            # Find contours in this mask
            contours, _ = cv2.findContours(blue_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            print(f"Found {len(contours)} blue regions")

            # Analyze largest contours
            large_contours = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 5000:  # Minimum area
                    x, y, w, h = cv2.boundingRect(contour)
                    large_contours.append({
                        'area': area,
                        'bounds': (x, y, w, h),
                        'aspect_ratio': w / h if h > 0 else 0
                    })

            # Sort by area
            large_contours.sort(key=lambda c: c['area'], reverse=True)

            print(f"Large regions (area > 5000): {len(large_contours)}")
            for j, contour in enumerate(large_contours[:3]):  # Show top 3
                x, y, w, h = contour['bounds']
                print(f"  #{j + 1}: {w}x{h} at ({x},{y}), area={contour['area']}, ratio={contour['aspect_ratio']:.2f}")

                # Check if this looks like minesweeper - more flexible for larger windows
                if w > 300 and h > 150 and 1.2 < contour['aspect_ratio'] < 3.0:
                    print(f"    🎯 This could be minesweeper! (good size and aspect ratio)")

    def interactive_detection(self):
        """Interactive detection with user feedback"""
        print("\n🎮 INTERACTIVE DETECTION")
        print("Let's try to detect the board and test positions step by step")

        # First try automatic detection
        if self.find_minesweeper_window():
            print("✅ Automatic detection found something!")
            print(f"📍 Detected region: {self.board_region}")
            print(f"📏 Cell size: {self.cell_size}")

            # Test the detection
            print("\n🧪 Testing detection accuracy...")
            self.test_corner_positions()

            # Ask user if it looks right
            response = input("\n❓ Did the mouse positions look correct? (y/n): ").lower()
            if response.startswith('y'):
                return True
            else:
                print("❌ Automatic detection failed, trying manual setup...")

        # Fall back to manual setup
        return self.manual_board_setup()

    def test_corner_positions(self):
        """Test if our position calculations are correct"""
        print("\n🎯 Testing corner positions...")

        test_positions = [
            (0, 0, "TOP-LEFT corner"),
            (0, 29, "TOP-RIGHT corner"),
            (15, 0, "BOTTOM-LEFT corner"),
            (15, 29, "BOTTOM-RIGHT corner"),
            (7, 14, "CENTER-ish"),
        ]

        for row, col, description in test_positions:
            pos = self.get_cell_position_precise(row, col)
            if pos:
                print(f"🎯 Moving to {description}: cell ({row},{col}) -> position {pos}")
                pyautogui.moveTo(pos[0], pos[1], duration=0.8)
                time.sleep(1.5)
            else:
                print(f"❌ Could not calculate position for {description}")

    def find_minesweeper_window(self):
        """Enhanced detection with better debugging"""
        try:
            print("🔍 Looking for Windows Minesweeper board...")

            screenshot = self.get_all_monitors_screenshot()
            screenshot_np = np.array(screenshot)

            # Try enhanced detection first
            return self.detect_minesweeper_with_color_analysis(screenshot_np)

        except Exception as e:
            print(f"❌ Error detecting board: {e}")
            return False

    def ensure_minesweeper_focus(self):
        """Ensure Minesweeper window has focus by doing a dummy click on the top-left cell"""
        print("🎯 Ensuring Minesweeper focus with dummy click on top-left cell...")

        # Get position of top-left cell (0,0)
        pos = self.get_cell_position_precise(0, 0)
        if pos:
            print(f"🎯 Dummy focus click at cell (0,0) position {pos}")
            # Just click without tracking it as a move
            pyautogui.click(pos[0], pos[1])
            time.sleep(0.1)  # Minimal time for focus to take effect
        else:
            print("⚠️ Cannot ensure focus - could not get top-left cell position")

    def click_cell(self, row, col, right_click=False, double_click=False):
        """Click on a specific cell with improved timing and verification"""

        # CRITICAL VALIDATION: Check if this move makes logical sense
        if not right_click:  # Only validate left clicks (safe moves)
            if self.validate_safe_move(row, col):
                pass  # Move is validated as safe
            else:
                print(f"🚨 CRITICAL: Refusing unsafe move at ({row}, {col}) - logic validation failed!")
                return False

        # CRITICAL SAFETY CHECK: Prevent flagging revealed cells
        if right_click:
            # Check if this cell is already revealed
            if (row, col) in self.revealed_cells:
                print(f"⚠️ Skipping flag on revealed cell ({row}, {col})")
                return False

            # Double-check with visual detection
            board_np = self.analyze_board_state()
            if board_np is not None:
                cell_info = self.get_cell_color_info(row, col, board_np)
                if cell_info and cell_info['is_revealed']:
                    print(f"⚠️ Skipping flag on visually revealed cell ({row}, {col})")
                    return False

        pos = self.get_cell_position_precise(row, col)
        if pos:
            # Move to position with slight delay for reliability
            pyautogui.moveTo(pos[0], pos[1], duration=0.1)  # Slower movement for reliability
            time.sleep(0.05)  # Pause after movement to ensure position is stable

            # Ensure the click happens with proper timing
            if double_click:
                pyautogui.doubleClick(pos[0], pos[1])
                print(f"🖱️ Double-clicked cell: ({row}, {col})")
                time.sleep(0.1)  # Longer pause for double-click processing
            elif right_click:
                pyautogui.rightClick(pos[0], pos[1])
                print(f"🚩 Right-clicked cell: ({row}, {col}) [FLAGGING]")
                time.sleep(0.15)  # Longer pause for flag to register

                # Add to tracking immediately - trust that the click worked
                self.flagged_cells.add((row, col))
                print(f"✅ Flag placed at ({row}, {col})")

                # Optional verification (but don't retry to avoid unflagging)
                # We'll verify in the next sync cycle instead
            else:
                pyautogui.leftClick(pos[0], pos[1])
                self.revealed_cells.add((row, col))
                time.sleep(0.08)  # Slightly longer pause for cell to update

                # Try to detect what number was revealed
                revealed_number = self.get_revealed_number_after_click(row, col)
                if revealed_number is not None:
                    if revealed_number == 0:
                        print(f"🖱️ Clicked cell: ({row}, {col}) -> BLANK")
                    else:
                        print(f"🖱️ Clicked cell: ({row}, {col}) -> {revealed_number}")
                else:
                    print(f"🖱️ Clicked cell: ({row}, {col})")

            # Invalidate screenshot cache after any click
            self._cached_board_np = None
            return True
        else:
            print(f"❌ Could not get position for cell ({row}, {col})")
            return False

    def verify_flag_placement(self, row, col):
        """Verify that a flag was actually placed at the specified cell"""
        try:
            # Capture current board state
            board_np = self.analyze_board_state()
            if board_np is None:
                return False

            # Check if the cell is now flagged
            cell_info = self.get_cell_color_info(row, col, board_np)
            if cell_info and cell_info.get('is_flagged', False):
                return True

            # Additional check using pattern recognition
            cell_info_pattern = self.get_cell_info_pattern_recognition(row, col, board_np)
            if cell_info_pattern and cell_info_pattern.get('is_flagged', False):
                return True

            return False

        except Exception as e:
            print(f"❌ Error verifying flag placement: {e}")
            return False

    def get_revealed_number_after_click(self, row, col):
        """Get the number revealed in a cell after clicking it"""
        try:
            board_np = self.analyze_board_state()
            if board_np is None:
                return None

            cell_info = self.get_cell_color_info(row, col, board_np)
            if cell_info and cell_info.get('is_revealed', False):
                number = cell_info.get('number', 0)
                return number if number is not None else 0
            return None
        except Exception as e:
            return None

    def analyze_board_state(self, force_refresh=False):
        """Capture and analyze the current board state with caching for performance"""
        if not self.board_region:
            return None

        # Check cache first (unless forced refresh)
        current_time = time.time()
        if not force_refresh and self._cached_board_np is not None:
            if current_time - self._cache_timestamp < self._cache_duration:
                return self._cached_board_np

        # Capture just the board area
        x, y, w, h = self.board_region

        screenshot = self.get_all_monitors_screenshot()

        # Crop to board region
        board_image = screenshot.crop((x, y, x + w, y + h))

        # Check if the image is mostly black (indicating a problem)
        board_np = np.array(board_image)

        # Cache the result
        self._cached_board_np = board_np
        self._cache_timestamp = current_time

        return board_np

    def get_cell_color_info(self, row, col, board_np=None):
        """Get color information for a specific cell using multiple sampling points"""
        if not self.board_region:
            return None

        # Use provided board screenshot or capture new one
        if board_np is None:
            board_np = self.analyze_board_state()
        if board_np is None:
            return None

        # Two-step approach: 1) Check edges for revealed/unrevealed, 2) If revealed, sample center for number
        try:
            # Calculate cell boundaries
            cell_left = col * self.cell_width
            cell_top = row * self.cell_height
            cell_right = cell_left + self.cell_width
            cell_bottom = cell_top + self.cell_height

            # STEP 1: Sample edges/corners to determine if cell is revealed or unrevealed
            edge_sample_points = [
                (cell_left + self.cell_width * 0.2, cell_top + self.cell_height * 0.2),    # Top-left area
                (cell_right - self.cell_width * 0.2, cell_top + self.cell_height * 0.2),   # Top-right area
                (cell_left + self.cell_width * 0.2, cell_bottom - self.cell_height * 0.2), # Bottom-left area
                (cell_right - self.cell_width * 0.2, cell_bottom - self.cell_height * 0.2), # Bottom-right area
                (cell_left + self.cell_width * 0.5, cell_top + self.cell_height * 0.1),    # Top edge
                (cell_left + self.cell_width * 0.1, cell_top + self.cell_height * 0.5),    # Left edge
            ]

            edge_colors = []
            for x, y in edge_sample_points:
                if 0 <= int(y) < board_np.shape[0] and 0 <= int(x) < board_np.shape[1]:
                    edge_colors.append(board_np[int(y), int(x)])

            if not edge_colors:
                return None

            # Determine revealed/unrevealed state from edge samples
            revealed_votes = 0
            edge_sample_colors = []

            for color in edge_colors:
                edge_sample_colors.append(tuple(color))
                if self.is_cell_revealed(color):
                    revealed_votes += 1

            # Use majority vote for revealed detection (more conservative)
            is_revealed = revealed_votes > len(edge_colors) / 2

            # Initialize flagged_votes for later use
            flagged_votes = 0

            # BETTER APPROACH: Use a two-step process with better logic
            cell_coord = (row, col)

            # Step 1: Check our internal tracking first (most reliable)
            if cell_coord in self.flagged_cells:
                is_flagged = True
                is_revealed = False  # Flagged cells cannot be revealed
            elif cell_coord in self.revealed_cells:
                is_flagged = False
                is_revealed = True  # Trust our tracking
            else:
                # Step 2: Only use visual detection for untracked cells
                # Check for flags ONLY in center area (where flag icon appears)
                center_x = cell_left + self.cell_width / 2
                center_y = cell_top + self.cell_height / 2

                center_flag_points = [
                    (center_x, center_y),           # Dead center
                    (center_x - 1, center_y),       # Slightly left
                    (center_x + 1, center_y),       # Slightly right
                    (center_x, center_y - 1),       # Slightly up
                    (center_x, center_y + 1),       # Slightly down
                ]

                flagged_votes = 0
                for x, y in center_flag_points:
                    if 0 <= int(y) < board_np.shape[0] and 0 <= int(x) < board_np.shape[1]:
                        color = board_np[int(y), int(x)]
                        if self.is_cell_flagged(color):
                            flagged_votes += 1

                # Only consider it flagged if we have strong evidence
                is_flagged = flagged_votes >= 2  # Need at least 2 samples to confirm flag

                if is_flagged:
                    # Check if this is actually a revealed number being misdetected as flag
                    center_x = cell_left + self.cell_width / 2
                    center_y = cell_top + self.cell_height / 2
                    if 0 <= int(center_y) < board_np.shape[0] and 0 <= int(center_x) < board_np.shape[1]:
                        center_color = board_np[int(center_y), int(center_x)]
                        r, g, b = int(center_color[0]), int(center_color[1]), int(center_color[2])

                        # Check if center has number colors (especially red "3")
                        if (r > 120 and g < 30 and b < 30):  # Red number colors
                            # Override the flag detection
                            is_flagged = False
                            is_revealed = True
                        elif (50 < r < 100 and 70 < g < 120 and 180 < b < 220):  # Blue "1" colors
                            is_flagged = False
                            is_revealed = True
                        else:
                            is_revealed = False
                    else:
                        is_revealed = False
                else:
                    # Use edge detection for revealed status (only if not flagged)
                    is_revealed = revealed_votes > len(edge_colors) / 2

            # STEP 2: If revealed, use FIXED PATTERN RECOGNITION for number detection
            detected_number = None
            center_sample_colors = []

            if is_revealed and not is_flagged:
                # ADAPTIVE LEARNING: Build templates from the game itself for 100% accuracy
                cell_image = self.extract_cell_image(board_np, row, col)

                if cell_image is not None:
                    detected_number = self.adaptive_number_detection(cell_image, row, col)

                    # CRITICAL SAFETY CHECK: Ensure we never return string values for revealed cells
                    if isinstance(detected_number, str):
                        print(f"🚨 CRITICAL ERROR: Number detection returned string '{detected_number}' for revealed cell ({row}, {col})")
                        print(f"🚨 This should never happen! Forcing to 0 (empty)")
                        detected_number = 0

                    # For debugging, still collect center color
                    center_x = cell_left + self.cell_width / 2
                    center_y = cell_top + self.cell_height / 2
                    if 0 <= int(center_y) < board_np.shape[0] and 0 <= int(center_x) < board_np.shape[1]:
                        center_sample_colors.append(tuple(board_np[int(center_y), int(center_x)]))
                else:
                    detected_number = 0  # Default to empty if can't extract

            # Use the first edge color for RGB representation
            representative_color = edge_colors[0]

            return {
                'rgb': tuple(representative_color),
                'position': (row, col),
                'is_revealed': is_revealed,
                'is_flagged': is_flagged,
                'number': detected_number,
                'edge_sample_colors': edge_sample_colors,  # For debugging edge detection
                'center_sample_colors': center_sample_colors,  # For debugging
                'votes': {'revealed': revealed_votes, 'flagged': flagged_votes, 'total': len(edge_colors)},
                'detection_method': 'fixed_pattern_recognition'  # Track detection method
            }
        except IndexError:
            return None

    def detect_number_using_fixed_pattern_recognition(self, cell_image, row, col):
        """FIXED pattern recognition that actually works for Minesweeper numbers"""
        try:
            # Convert to grayscale for analysis
            gray = cv2.cvtColor(cell_image, cv2.COLOR_RGB2GRAY)
            h, w = gray.shape

            # Check if there are any dark pixels (numbers are darker than background)
            dark_threshold = 150
            dark_pixels = gray < dark_threshold
            dark_ratio = np.sum(dark_pixels) / dark_pixels.size

            # Store for final sanity check
            self._last_dark_ratio = dark_ratio

            # If no significant dark pixels, it's empty
            # FIXED: Restore more conservative threshold to avoid false positives
            if dark_ratio < 0.05:
                return 0  # Empty cell
            elif dark_ratio < 0.15:
                # Continue with analysis but be very conservative
                # Only check for blue "1" if we have some dark pixels but not too many
                if 0.05 <= dark_ratio < 0.12:
                    blue_check = self.check_for_blue_one(cell_image)
                    if blue_check:
                        return 1

            # Create binary image for shape analysis
            _, binary = cv2.threshold(gray, 140, 255, cv2.THRESH_BINARY_INV)

            # Find contours
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return 0  # No shapes found

            # Get the main contour (largest area)
            main_contour = max(contours, key=cv2.contourArea)
            area = cv2.contourArea(main_contour)

            if area < 10:  # Too small to be a number
                return 0

            # Get bounding rectangle
            x, y, cw, ch = cv2.boundingRect(main_contour)
            aspect_ratio = cw / ch if ch > 0 else 0

            # First, try color detection to see what we find
            center_colors = []
            cy, cx = h//2, w//2
            for dy in range(-3, 4):
                for dx in range(-3, 4):
                    if 0 <= cy+dy < h and 0 <= cx+dx < w:
                        center_colors.append(cell_image[cy+dy, cx+dx])

            # Check if this is a red number (3, 5, 7, 8) - use pattern recognition
            has_red_color = False
            for color in center_colors[:10]:  # Sample fewer colors for red check
                r, g, b = int(color[0]), int(color[1]), int(color[2])
                if r > 80 and r > g + 30 and r > b + 20:  # General red detection
                    has_red_color = True
                    break

            if has_red_color:
                return self.detect_red_number_by_improved_color(center_colors, aspect_ratio, area, row, col)

            # Fast color detection for distinctive numbers
            color_votes = {}
            for color in center_colors:
                number = self.get_cell_number(color)
                if number is not None and number in [1, 2, 4, 6]:
                    color_votes[number] = color_votes.get(number, 0) + 1

            if color_votes:
                detected_number = max(color_votes.keys(), key=lambda k: color_votes[k])
                # Basic validation
                if detected_number == 1 and aspect_ratio > 1.5:
                    return 0
                return detected_number

            # Very simple shape-based fallback
            if aspect_ratio < 0.5:  # Very narrow
                return 1
            elif aspect_ratio > 1.2:  # Very wide
                return 4  # Default to 4 for wide shapes
            else:  # Medium aspect ratio
                if area > 60:
                    return 8
                else:
                    return 3

            # FINAL SANITY CHECK: If we detected a number but dark ratio is very low, override
            if hasattr(self, '_last_dark_ratio') and self._last_dark_ratio < 0.1:
                return 0

            # If all detection methods fail, assume empty cell
            return 0

        except Exception as e:
            return 0

    def detect_red_number_by_pattern(self, cell_image, gray, aspect_ratio, area, row, col):
        """Specialized pattern recognition for red numbers (3, 5, 7, 8)"""
        try:
            # Create binary image for shape analysis
            _, binary = cv2.threshold(gray, 140, 255, cv2.THRESH_BINARY_INV)

            # Find contours
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return 0

            # Get the main contour
            main_contour = max(contours, key=cv2.contourArea)
            x, y, w, h = cv2.boundingRect(main_contour)

            # IMPROVED: More specific shape analysis for red numbers

            # Calculate key measurements
            top_quarter = binary[y:y+h//4, x:x+w]
            middle_half = binary[y+h//4:y+3*h//4, x:x+w]
            bottom_quarter = binary[y+3*h//4:y+h, x:x+w]
            left_half = binary[y:y+h, x:x+w//2]
            right_half = binary[y:y+h, x+w//2:x+w]

            top_pixels = np.sum(top_quarter > 0)
            middle_pixels = np.sum(middle_half > 0)
            bottom_pixels = np.sum(bottom_quarter > 0)
            left_pixels = np.sum(left_half > 0)
            right_pixels = np.sum(right_half > 0)
            total_pixels = np.sum(binary > 0)

            # Calculate hole detection more carefully
            filled = binary.copy()
            cv2.floodFill(filled, None, (0, 0), 255)
            holes = cv2.bitwise_not(filled)
            hole_pixels = np.sum(holes > 0)

            # "7" - Wide, top-heavy, simple shape (check FIRST)
            if aspect_ratio > 1.0 and top_pixels > bottom_pixels * 2.5 and top_pixels > total_pixels * 0.5:
                return 7

            # "5" - Top-heavy with horizontal line at top (check SECOND)
            if top_pixels > bottom_pixels * 2.0 and aspect_ratio < 0.8:
                # Check for strong horizontal line at very top
                top_line = binary[y:y+max(1, h//8), x:x+w]
                horizontal_coverage = np.sum(top_line > 0) / max(1, w)
                if horizontal_coverage > 0.7:  # Very strong horizontal line
                    return 5

            # "3" - Right-heavy with curves (EXPANDED criteria since it's more common)
            if right_pixels > left_pixels * 1.2 and 0.3 < aspect_ratio < 1.0:
                return 3

            # Additional "3" fallback - if it has any right bias at all
            if right_pixels > left_pixels and 0.4 < aspect_ratio < 0.9:
                return 3

            # "8" - EXTREMELY restrictive (only if 100% certain)
            # 8 is so rare that we should almost never detect it
            if (hole_pixels > 40 and total_pixels > 100 and
                0.7 < aspect_ratio < 1.1 and
                abs(left_pixels - right_pixels) < total_pixels * 0.15 and
                abs(top_pixels - bottom_pixels) < total_pixels * 0.15 and
                middle_pixels > total_pixels * 0.3):  # Must have substantial middle section
                return 8

            # If we detected holes but not confident it's "8", it's probably "3" with artifacts
            if hole_pixels > 10:
                return 3

            # Sample center colors and try color detection
            center_colors = []
            cy, cx = gray.shape[0]//2, gray.shape[1]//2
            for dy in range(-2, 3):
                for dx in range(-2, 3):
                    if 0 <= cy+dy < gray.shape[0] and 0 <= cx+dx < gray.shape[1]:
                        center_colors.append(cell_image[cy+dy, cx+dx])

            # Try color detection on red numbers specifically
            for color in center_colors:
                number = self.get_cell_number(color)
                if number in [3, 5, 7, 8]:  # Only accept red numbers
                    return number

            # Final fallback based on shape characteristics (NEVER default to 8)
            if aspect_ratio < 0.7 and top_pixels > bottom_pixels:
                return 5
            elif aspect_ratio > 1.1 and top_pixels > middle_pixels:
                return 7
            else:
                return 3

        except Exception as e:
            return 3  # Default to 3 for red numbers

    def detect_red_number_by_improved_color(self, center_colors, aspect_ratio, area, row, col):
        """Fast red number detection"""
        try:
            color_votes = {}

            for color in center_colors:
                r, g, b = int(color[0]), int(color[1]), int(color[2])

                if r < 80 or r <= g + 20 or r <= b + 20:
                    continue

                detected_number = None

                # Fast red number detection
                if 100 < r < 140 and g < 5 and b < 5:
                    detected_number = 5
                elif 150 < r < 180 and g < 15 and b < 15:
                    detected_number = 3
                elif 170 < r < 190 and 5 < g < 15 and 5 < b < 15:
                    detected_number = 7
                elif 160 < r < 180 and g < 5 and 15 < b < 25:
                    detected_number = 8

                if detected_number:
                    color_votes[detected_number] = color_votes.get(detected_number, 0) + 1

            if color_votes:
                # Anti-8 bias
                if 8 in color_votes and (color_votes[8] <= 10 or len(color_votes) > 1):
                    del color_votes[8]

                if color_votes:
                    return max(color_votes.keys(), key=lambda k: color_votes[k])

            # Fast fallback
            return 5 if aspect_ratio < 0.7 else (7 if aspect_ratio > 1.1 else 3)

        except:
            return 3

    def extract_cell_image(self, board_np, row, col):
        """Extract the cell image for pattern recognition"""
        try:
            # Calculate cell boundaries
            cell_left = int(col * self.cell_width)
            cell_top = int(row * self.cell_height)
            cell_right = int(cell_left + self.cell_width)
            cell_bottom = int(cell_top + self.cell_height)

            # Ensure bounds are valid
            board_h, board_w = board_np.shape[:2]
            cell_left = max(0, min(cell_left, board_w))
            cell_top = max(0, min(cell_top, board_h))
            cell_right = max(0, min(cell_right, board_w))
            cell_bottom = max(0, min(cell_bottom, board_h))

            if cell_right <= cell_left or cell_bottom <= cell_top:
                return None

            # Extract cell image
            cell_image = board_np[cell_top:cell_bottom, cell_left:cell_right]

            if cell_image.size == 0:
                return None

            return cell_image

        except Exception as e:
            return None

    def adaptive_number_detection(self, cell_image, row, col):
        """Adaptive detection that learns from the game itself"""
        try:
            # Initialize template storage if not exists
            if not hasattr(self, 'number_templates'):
                self.number_templates = {}
                self.template_confidence = {}
                # Try to load captured templates
                self.load_captured_templates()

            # Convert to grayscale
            gray = cv2.cvtColor(cell_image, cv2.COLOR_RGB2GRAY)
            h, w = gray.shape

            # Quick empty cell check - but be more conservative to avoid missing numbers
            avg_brightness = np.mean(gray)
            if avg_brightness > 210:  # Balanced threshold to avoid missing numbers but catch truly empty cells
                return 0

            # Create a normalized version for template matching with better aspect ratio preservation
            # Use the actual cell dimensions for better normalization
            target_size = min(20, max(h, w))  # Adaptive size based on actual cell size
            if h > w:
                new_h, new_w = target_size, int(target_size * w / h)
            else:
                new_h, new_w = int(target_size * h / w), target_size

            # Ensure minimum size
            new_h, new_w = max(new_h, 8), max(new_w, 8)
            normalized = cv2.resize(gray, (new_w, new_h))

            # Try comprehensive template matching (numbers + special types)
            best_match = None
            best_score = 0
            best_type = None

            if self.number_templates:
                # Template matching with automatic size adjustment
                for template_key, templates in self.number_templates.items():
                    for i, template in enumerate(templates):
                        # Automatically resize template to match normalized cell if needed
                        if template.shape != normalized.shape:
                            template_resized = cv2.resize(template, (normalized.shape[1], normalized.shape[0]))
                        else:
                            template_resized = template

                        result = cv2.matchTemplate(normalized, template_resized, cv2.TM_CCOEFF_NORMED)
                        score = np.max(result)

                        # Removed verbose debug messages to avoid cluttering board display

                        # TEMPORARY FIX: Use same logic as capture script (no threshold)
                        # This will help us determine if thresholds are the issue
                        threshold = 0.0  # No threshold - just like capture script

                        # Original adaptive threshold logic (commented out for testing)
                        # size_similarity = min(normalized.shape) / max(template_resized.shape)
                        # if size_similarity < 0.8:
                        #     threshold = 0.75  # Higher threshold for very different sizes
                        # elif size_similarity < 0.95:
                        #     threshold = 0.65  # Medium threshold for somewhat different sizes
                        # else:
                        #     threshold = 0.6   # Lower threshold for similar sizes

                        if score > best_score and score > threshold:
                            best_score = score
                            best_match = template_key
                            best_type = 'number' if isinstance(template_key, int) else 'special'

                if best_match is not None:
                    # Handle special template types
                    if best_type == 'special':
                        if best_match == 'empty':
                            return 0
                        elif best_match == 'flag':
                            # CRITICAL FIX: If we're in this function, the cell is already detected as REVEALED
                            # A revealed cell should NEVER be a flag! This is a template matching error.
                            # Ignore this match and continue with fallback detection
                            pass  # Continue to fallback detection
                        elif best_match == 'unrevealed':
                            # CRITICAL FIX: If we're in this function, the cell is already detected as REVEALED
                            # So we should never return 'UNREVEALED' - treat it as empty instead
                            return 0
                    else:
                        return best_match

            # If no template match, use color detection (fast, no debug)
            detected_number = self.fallback_color_detection(cell_image, row, col)

            # Special check for blue "1"s that might be missed
            if detected_number == 0:
                # Check if this might be a blue "1" that was missed
                blue_check = self.check_for_blue_one(cell_image)
                if blue_check:
                    return 1

            # CRITICAL SAFETY CHECK: Never return string values for revealed cells
            if isinstance(detected_number, str):
                print(f"🚨 CRITICAL ERROR: adaptive_number_detection returned string '{detected_number}' for revealed cell ({row}, {col})")
                print(f"🚨 This should never happen! Forcing to 0 (empty)")
                return 0

            final_result = detected_number if detected_number is not None else 0

            # Log detection results to a debug file for later analysis (won't clutter console)
            try:
                with open("detection_debug.log", "a") as f:
                    f.write(f"Cell ({row},{col}): template_match={best_match if 'best_match' in locals() else 'None'}, "
                           f"fallback={detected_number}, final={final_result}\n")
            except:
                pass  # Don't let logging errors break the game

            return final_result

        except Exception as e:
            print(f"❌ Error in adaptive detection for ({row}, {col}): {e}")
            return 0

    def validate_safe_move(self, row, col):
        """Validate that a safe move is actually safe based on current number detection"""
        try:
            # Check all revealed neighbors to see if this move contradicts any numbers
            for dr in [-1, 0, 1]:
                for dc in [-1, 0, 1]:
                    if dr == 0 and dc == 0:
                        continue
                    nr, nc = row + dr, col + dc
                    if 0 <= nr < self.board_height and 0 <= nc < self.board_width:
                        if (nr, nc) in self.revealed_cells:
                            # This neighbor is revealed - check if our move makes sense
                            neighbor_number = self.revealed_cells[(nr, nc)]
                            if isinstance(neighbor_number, int) and neighbor_number > 0:
                                # Count current flags and unrevealed cells around this neighbor
                                neighbor_flags = 0
                                neighbor_unrevealed = 0
                                for dr2 in [-1, 0, 1]:
                                    for dc2 in [-1, 0, 1]:
                                        if dr2 == 0 and dc2 == 0:
                                            continue
                                        nr2, nc2 = nr + dr2, nc + dc2
                                        if 0 <= nr2 < self.board_height and 0 <= nc2 < self.board_width:
                                            if (nr2, nc2) in self.flagged_cells:
                                                neighbor_flags += 1
                                            elif (nr2, nc2) not in self.revealed_cells:
                                                neighbor_unrevealed += 1

                                # If this neighbor already has enough flags, clicking (row,col) should be safe
                                if neighbor_flags >= neighbor_number:
                                    continue  # This constraint is satisfied

                                # If clicking (row,col) would leave too few unrevealed cells for remaining mines
                                remaining_mines = neighbor_number - neighbor_flags
                                remaining_unrevealed = neighbor_unrevealed - (1 if (row, col) not in self.revealed_cells else 0)
                                if remaining_mines > remaining_unrevealed:
                                    print(f"🚨 LOGIC ERROR: Cell ({row},{col}) cannot be safe - neighbor ({nr},{nc}) has number {neighbor_number}, {neighbor_flags} flags, would leave {remaining_unrevealed} unrevealed for {remaining_mines} mines")
                                    return False

            return True  # No logical contradictions found

        except Exception as e:
            print(f"❌ Error in move validation: {e}")
            return True  # If validation fails, allow the move (don't break the game)

    def debug_logic_for_cell(self, row, col, board_np=None):
        """Debug the logical deduction for a specific numbered cell"""
        try:
            if board_np is None:
                board_np = self.analyze_board_state()

            cell_number = self.get_cell_number_safe(row, col, board_np)
            if cell_number is None or not isinstance(cell_number, int) or cell_number <= 0:
                print(f"🔍 Cell ({row},{col}): Not a numbered cell (detected: {cell_number})")
                return

            print(f"\n🔍 LOGIC DEBUG FOR CELL ({row},{col}) = {cell_number}")
            print("=" * 50)

            # Get neighbors
            unrevealed_neighbors = self.get_unrevealed_neighbors(row, col, board_np)
            flagged_neighbors = self.get_flagged_neighbors(row, col, board_np)

            print(f"📊 Neighbor Analysis:")
            print(f"   Cell number: {cell_number}")
            print(f"   Unrevealed neighbors: {len(unrevealed_neighbors)} -> {unrevealed_neighbors}")
            print(f"   Flagged neighbors: {len(flagged_neighbors)} -> {flagged_neighbors}")

            # Check each neighbor individually
            print(f"\n🔍 Individual Neighbor Check:")
            all_neighbors = self.get_neighbors(row, col)
            for nr, nc in all_neighbors:
                # Check our tracking
                in_revealed = (nr, nc) in self.revealed_cells
                in_flagged = (nr, nc) in self.flagged_cells

                # Check visual state
                cell_info = self.get_cell_color_info(nr, nc, board_np)
                if cell_info:
                    visual_revealed = cell_info['is_revealed']
                    visual_flagged = cell_info['is_flagged']
                    visual_number = cell_info.get('number', 'None')
                else:
                    visual_revealed = "Unknown"
                    visual_flagged = "Unknown"
                    visual_number = "Unknown"

                print(f"   ({nr},{nc}): Track(R:{in_revealed}, F:{in_flagged}) Visual(R:{visual_revealed}, F:{visual_flagged}, N:{visual_number})")

            # Apply logic rules
            print(f"\n🧠 Logic Rule Analysis:")

            # Rule 1: If flagged neighbors = cell number, remaining are safe
            if len(flagged_neighbors) == cell_number:
                print(f"   ✅ RULE 1: {len(flagged_neighbors)} flags = {cell_number} -> {len(unrevealed_neighbors)} unrevealed are SAFE")
                print(f"      Safe cells: {unrevealed_neighbors}")

            # Rule 2: If unrevealed neighbors = cell number (no flags), all are mines
            elif len(flagged_neighbors) == 0 and len(unrevealed_neighbors) == cell_number:
                print(f"   ✅ RULE 2: 0 flags, {len(unrevealed_neighbors)} unrevealed = {cell_number} -> all unrevealed are MINES")
                print(f"      Mine cells: {unrevealed_neighbors}")

            # Rule 3: If flagged + unrevealed = cell number, unrevealed are mines
            elif len(flagged_neighbors) + len(unrevealed_neighbors) == cell_number:
                print(f"   ✅ RULE 3: {len(flagged_neighbors)} flags + {len(unrevealed_neighbors)} unrevealed = {cell_number} -> unrevealed are MINES")
                print(f"      Mine cells: {unrevealed_neighbors}")

            else:
                print(f"   ❌ NO RULE APPLIES:")
                print(f"      Flags ({len(flagged_neighbors)}) ≠ Number ({cell_number})")
                print(f"      Unrevealed ({len(unrevealed_neighbors)}) ≠ Number ({cell_number})")
                print(f"      Flags + Unrevealed ({len(flagged_neighbors) + len(unrevealed_neighbors)}) ≠ Number ({cell_number})")

            print("=" * 50)

        except Exception as e:
            print(f"❌ Error in logic debug: {e}")

    def debug_all_active_logic(self):
        """Debug logic for all active numbered cells"""
        try:
            board_np = self.analyze_board_state()
            if board_np is None:
                print("❌ Could not capture board state")
                return

            self.update_active_numbered_cells(board_np)

            print(f"\n🔍 DEBUGGING ALL ACTIVE LOGIC")
            print(f"Active numbered cells: {len(self.active_numbered_cells)}")

            for row, col in sorted(self.active_numbered_cells):
                self.debug_logic_for_cell(row, col, board_np)

        except Exception as e:
            print(f"❌ Error in debug all logic: {e}")

    def check_for_blue_one(self, cell_image):
        """Special check for blue "1"s that might be missed by other detection methods"""
        try:
            h, w = cell_image.shape[:2]

            # Sample multiple points in the cell
            blue_pixels = 0
            green_pixels = 0
            total_samples = 0
            very_light_pixels = 0

            for y in range(h//4, 3*h//4, 2):  # Sample middle area
                for x in range(w//4, 3*w//4, 2):
                    if 0 <= y < h and 0 <= x < w:
                        r, g, b = cell_image[y, x]
                        r, g, b = int(r), int(g), int(b)
                        total_samples += 1

                        # Check for very light pixels (background/blank cells)
                        if r > 180 and g > 180 and b > 180:
                            very_light_pixels += 1

                        # Check for green color (might be "2")
                        if g > 70 and g > r + 15 and g > b + 15:
                            green_pixels += 1

                        # Check for blue color characteristic of "1" - MORE STRICT
                        if b > 150 and b > r + 50 and b > g + 50:  # Much more strict blue detection
                            blue_pixels += 1

            if total_samples > 0:
                blue_ratio = blue_pixels / total_samples
                green_ratio = green_pixels / total_samples
                light_ratio = very_light_pixels / total_samples

                # Don't detect as "1" if:
                # 1. Too many light pixels (blank cell)
                # 2. Any green pixels (might be "2")
                # 3. Not enough blue pixels
                if light_ratio > 0.7:  # Mostly light = blank cell
                    return False
                if green_ratio > 0.05:  # Any significant green = not "1"
                    return False
                if blue_ratio < 0.2:  # Need substantial blue evidence
                    return False

                return True

            return False

        except Exception as e:
            return False

    def deduce_number_from_context(self, cell_image, row, col):
        """Use logical context to deduce the number"""
        try:
            # Simplified context deduction without screenshot
            # Count neighboring flagged cells (known mines)
            flagged_neighbors = 0

            for dr in [-1, 0, 1]:
                for dc in [-1, 0, 1]:
                    if dr == 0 and dc == 0:
                        continue
                    nr, nc = row + dr, col + dc
                    if 0 <= nr < self.board_height and 0 <= nc < self.board_width:
                        if (nr, nc) in self.flagged_cells:
                            flagged_neighbors += 1

            # If we have some flagged neighbors, this gives us a clue
            if flagged_neighbors > 0 and flagged_neighbors <= 8:
                print(f"🧠 Cell ({row}, {col}): Has {flagged_neighbors} flagged neighbors")
                # This is a hint but not definitive - return None to use other methods
                return None

            return None

        except Exception as e:
            print(f"❌ Error in context deduction: {e}")
            return None

    def store_number_template(self, template, number):
        """Store a template for future matching - DISABLED until 100% accuracy achieved"""
        try:
            # DISABLED: Don't create templates from potentially incorrect bot detections
            print(f"📚 Template storage DISABLED - use manual capture script only")
            print(f"📚 Bot wanted to store template for number {number} but this is disabled for accuracy")
            return

            # Original code commented out to prevent incorrect template creation
            # if number not in self.number_templates:
            #     self.number_templates[number] = []
            #     self.template_confidence[number] = []
            #
            # if len(self.number_templates[number]) < 5:
            #     self.number_templates[number].append(template.copy())
            #     self.template_confidence[number].append(1.0)

        except Exception as e:
            print(f"❌ Error in template storage: {e}")

    def load_captured_templates(self, template_file="minesweeper_templates.pkl"):
        """Load captured templates from the template capture script"""
        try:
            import pickle
            import os

            if os.path.exists(template_file):
                with open(template_file, 'rb') as f:
                    templates = pickle.load(f)

                # Load templates into bot's storage
                self.number_templates = templates
                self.template_confidence = {}

                # Initialize confidence scores
                for template_key, template_list in templates.items():
                    self.template_confidence[template_key] = [1.0] * len(template_list)

                # Separate number and special templates for display
                numbers = [k for k in templates.keys() if isinstance(k, int)]
                specials = [k for k in templates.keys() if isinstance(k, str)]

                total_templates = sum(len(template_list) for template_list in templates.values())
                print(f"✅ Loaded {total_templates} captured templates:")

                if numbers:
                    for number in sorted(numbers):
                        print(f"   Number {number}: {len(templates[number])} templates")

                if specials:
                    for special in sorted(specials):
                        print(f"   Special '{special}': {len(templates[special])} templates")

                return True
            else:
                print(f"📂 No captured templates found: {template_file}")
                return False

        except Exception as e:
            print(f"❌ Error loading captured templates: {e}")
            return False

    def fallback_color_detection(self, cell_image, row, col):
        """Fast color detection fallback with improved sampling for "2" detection"""
        try:
            h, w = cell_image.shape[:2]
            # Sample more points to catch green "2" colors better
            center_y, center_x = h//2, w//2
            samples = []

            # Expanded sampling pattern for better color detection
            for dy in range(-2, 3):
                for dx in range(-2, 3):
                    y, x = center_y + dy, center_x + dx
                    if 0 <= y < h and 0 <= x < w:
                        samples.append(cell_image[y, x])

            # Fast voting with debug for "2" detection issues
            votes = {}
            green_samples = 0
            blue_samples = 0
            light_samples = 0

            for sample in samples:
                r, g, b = int(sample[0]), int(sample[1]), int(sample[2])

                # Check for very light colors (blank cells)
                if r > 180 and g > 180 and b > 180:
                    light_samples += 1

                # Debug green detection specifically
                if g > 70 and g > r + 10 and g > b + 10:
                    green_samples += 1

                # Debug blue detection for "1"s - MORE STRICT
                if b > 150 and b > r + 40 and b > g + 40:  # Much more strict
                    blue_samples += 1

                number = self.get_cell_number(sample)
                # CRITICAL FIX: Only count valid integer numbers, never strings like 'FLAG'
                if number is not None and isinstance(number, int):
                    votes[number] = votes.get(number, 0) + 1

            # Don't force detections if we have mostly light samples (blank cell)
            if light_samples > len(samples) * 0.6:  # More than 60% light = blank
                return 0

            # If we found green samples but no "2" votes, there might be a detection issue
            if green_samples > 3 and 2 not in votes and blue_samples == 0:  # Only if no blue conflict
                # Force a "2" detection if we have strong green evidence
                votes[2] = green_samples

            # If we found blue samples but no "1" votes, there might be a detection issue
            # BUT be more careful - need strong evidence and no green conflict
            if blue_samples > 5 and 1 not in votes and green_samples == 0:  # Higher threshold and no green
                # Force a "1" detection if we have strong blue evidence
                votes[1] = blue_samples

            return max(votes.keys(), key=lambda k: votes[k]) if votes else 0

        except Exception as e:
            print(f"❌ Error in fallback color detection for ({row}, {col}): {e}")
            return 0

    def debug_number_detection_for_cell(self, row, col):
        """Debug number detection for a specific cell that's showing as BLANK"""
        try:
            board_np = self.analyze_board_state()
            if board_np is None:
                print(f"❌ Could not capture board state for debugging cell ({row}, {col})")
                return

            cell_info = self.get_cell_color_info(row, col, board_np)
            if not cell_info:
                print(f"❌ Could not get cell info for ({row}, {col})")
                return

            print(f"\n🔍 DEBUGGING NUMBER DETECTION FOR CELL ({row}, {col})")
            print("=" * 60)
            print(f"Cell state: revealed={cell_info['is_revealed']}, flagged={cell_info['is_flagged']}")
            print(f"Detected number: {cell_info['number']}")

            if cell_info['is_revealed'] and not cell_info['is_flagged']:
                # Extract cell image and test detection methods
                cell_image = self.extract_cell_image(board_np, row, col)
                if cell_image is not None:
                    print(f"Cell image shape: {cell_image.shape}")

                    # Test color detection on center samples
                    h, w = cell_image.shape[:2]
                    center_y, center_x = h//2, w//2

                    print(f"Center color samples:")
                    for dy in range(-2, 3):
                        for dx in range(-2, 3):
                            y, x = center_y + dy, center_x + dx
                            if 0 <= y < h and 0 <= x < w:
                                color = cell_image[y, x]
                                r, g, b = int(color[0]), int(color[1]), int(color[2])
                                detected_num = self.get_cell_number(color)
                                print(f"  ({dx:+2d},{dy:+2d}): RGB({r:3d},{g:3d},{b:3d}) -> {detected_num}")

                                # Check green criteria specifically
                                if g > 70 and g > r + 10 and g > b + 10:
                                    print(f"    ✅ Meets green criteria for '2'")
                                elif g > 80:
                                    print(f"    ⚠️ High green but doesn't meet dominance criteria")

                    # Test adaptive detection
                    adaptive_result = self.adaptive_number_detection(cell_image, row, col)
                    print(f"Adaptive detection result: {adaptive_result}")

                    # Test fallback detection
                    fallback_result = self.fallback_color_detection(cell_image, row, col)
                    print(f"Fallback detection result: {fallback_result}")

            print("=" * 60)

        except Exception as e:
            print(f"❌ Error in debug function: {e}")



    def template_match_number_precise(self, cell_image, row, col):
        """Precise template matching for 100% accurate number detection"""
        try:
            print(f"🎯 Cell ({row}, {col}): Using precise template matching")

            # Convert to grayscale for template matching
            gray = cv2.cvtColor(cell_image, cv2.COLOR_RGB2GRAY)
            h, w = gray.shape

            # Check if cell is mostly light (empty cell)
            avg_brightness = np.mean(gray)
            if avg_brightness > 200:
                print(f"🎯 Cell ({row}, {col}): Very bright (avg={avg_brightness:.1f}) -> EMPTY")
                return 0

            # Create templates for each number based on known characteristics
            # This is a simplified approach - in practice you'd use actual templates

            # Look for distinctive patterns
            # Convert to binary for pattern analysis
            _, binary = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY_INV)

            # Count dark pixels
            dark_pixels = np.sum(binary > 0)
            dark_ratio = dark_pixels / (h * w)

            print(f"🎯 Cell ({row}, {col}): Dark pixels={dark_pixels}, ratio={dark_ratio:.3f}")

            if dark_ratio < 0.1:
                print(f"🎯 Cell ({row}, {col}): Low dark ratio -> EMPTY")
                return 0

            # Find contours for shape analysis
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                print(f"🎯 Cell ({row}, {col}): No contours -> EMPTY")
                return 0

            # Get main contour
            main_contour = max(contours, key=cv2.contourArea)
            area = cv2.contourArea(main_contour)

            if area < 20:
                print(f"🎯 Cell ({row}, {col}): Small area ({area}) -> EMPTY")
                return 0

            # Get bounding box
            x, y, cw, ch = cv2.boundingRect(main_contour)
            aspect_ratio = cw / ch if ch > 0 else 0

            print(f"🎯 Cell ({row}, {col}): Contour area={area}, aspect={aspect_ratio:.2f}")

            # Use color analysis for final determination
            center_colors = []
            cy, cx = h//2, w//2
            for dy in range(-2, 3):
                for dx in range(-2, 3):
                    if 0 <= cy+dy < h and 0 <= cx+dx < w:
                        center_colors.append(cell_image[cy+dy, cx+dx])

            # Analyze colors to determine number
            color_analysis = self.analyze_colors_for_number(center_colors, row, col)

            if color_analysis is not None:
                print(f"🎯 Cell ({row}, {col}): Color analysis result -> {color_analysis}")
                return color_analysis

            # If all else fails, this needs manual intervention
            print(f"❌ Cell ({row}, {col}): Could not determine number - MANUAL INTERVENTION NEEDED")
            return None  # Force manual intervention

        except Exception as e:
            print(f"❌ Error in precise template matching for ({row}, {col}): {e}")
            return None

    def analyze_colors_for_number(self, colors, row, col):
        """Analyze colors to determine number with high precision"""
        try:
            # Count color types
            blue_count = 0
            green_count = 0
            red_count = 0
            navy_count = 0

            for color in colors:
                r, g, b = int(color[0]), int(color[1]), int(color[2])

                # IMPROVED: More specific color detection to distinguish 1 vs 4
                # Check for Navy "4" FIRST (most restrictive)
                if r <= 20 and g <= 20 and b >= 120:  # Very dark navy blue
                    navy_count += 1
                    print(f"🔍 Cell ({row}, {col}): Navy detected RGB({r}, {g}, {b})")
                # Then check for bright Blue "1"
                elif 50 <= r <= 120 and 70 <= g <= 150 and 180 <= b <= 240 and b > r + 60:  # Bright blue
                    blue_count += 1
                    print(f"🔍 Cell ({row}, {col}): Bright blue detected RGB({r}, {g}, {b})")
                # Green "2"
                elif 20 <= r <= 60 and 80 <= g <= 130 and g > r + 20 and b <= 30:
                    green_count += 1
                    print(f"🔍 Cell ({row}, {col}): Green detected RGB({r}, {g}, {b})")
                # Red "3"
                elif r >= 140 and g <= 30 and b <= 30:
                    red_count += 1
                    print(f"🔍 Cell ({row}, {col}): Red detected RGB({r}, {g}, {b})")

            print(f"🎯 Cell ({row}, {col}): Color counts - Blue:{blue_count}, Green:{green_count}, Red:{red_count}, Navy:{navy_count}")

            # Determine number based on dominant color
            if blue_count >= 3:
                return 1
            elif green_count >= 3:
                return 2
            elif red_count >= 3:
                return 3
            elif navy_count >= 3:
                return 4
            elif blue_count >= 1 and blue_count > green_count and blue_count > red_count:
                return 1
            elif green_count >= 1 and green_count > blue_count and green_count > red_count:
                return 2
            elif red_count >= 1:
                return 3  # Red is distinctive
            elif navy_count >= 1:
                return 4  # Navy is distinctive
            else:
                return 0  # Empty

        except Exception as e:
            print(f"❌ Error analyzing colors: {e}")
            return None

    def detect_number_using_pattern_recognition(self, cell_image, row, col):
        """Detect number using pattern recognition instead of color"""
        try:
            # Convert to grayscale for pattern analysis
            gray = cv2.cvtColor(cell_image, cv2.COLOR_RGB2GRAY)

            # Method 1: Check if there are any dark pixels (numbers are darker than background)
            dark_threshold = 150
            dark_pixels = gray < dark_threshold
            dark_ratio = np.sum(dark_pixels) / dark_pixels.size

            # If no significant dark pixels, it's probably empty
            if dark_ratio < 0.05:
                return 0  # Empty cell

            # Method 2: Use shape analysis for number detection
            detected_number = self.simple_shape_analysis(cell_image, gray)
            if detected_number is not None:
                print(f"🔍 Pattern recognition detected number {detected_number} at ({row}, {col})")
                return detected_number

            # Method 3: Try template matching as backup
            detected_number = self.template_match_numbers(cell_image, gray)
            if detected_number is not None:
                print(f"🔍 Template matching detected number {detected_number} at ({row}, {col})")
                return detected_number

            # Method 4: Try red number specialized analysis
            detected_number = self.analyze_red_number_shapes(cell_image, gray)
            if detected_number is not None:
                print(f"🔍 Red number analysis detected number {detected_number} at ({row}, {col})")
                return detected_number

            # If all pattern recognition fails, assume empty cell
            print(f"❓ Pattern recognition could not identify number at ({row}, {col}) - assuming empty")
            return 0

        except Exception as e:
            print(f"❌ Error in pattern recognition for cell ({row}, {col}): {e}")
            return 0  # Default to empty cell on error

    def is_cell_revealed(self, rgb_color):
        """Check if a cell is revealed based on its color"""
        # Convert to regular Python integers to avoid numpy overflow warnings
        r, g, b = int(rgb_color[0]), int(rgb_color[1]), int(rgb_color[2])

        # If it's very blue, it's definitely unrevealed
        if b > r + 30 and b > g + 30 and b > 150:
            return False

        # If it's very dark blue (typical unrevealed), it's unrevealed
        if b > 100 and r < 80 and g < 80:
            return False

        # Revealed cells are typically light gray/white or have light backgrounds
        # This includes numbered cells which have gray/white backgrounds
        gray_threshold = 120  # Lowered threshold to catch more revealed cells

        # If all RGB values are reasonably high, it's likely revealed
        if r > gray_threshold and g > gray_threshold and b > gray_threshold:
            return True

        # Additional check for light grayish cells (revealed empty cells)
        if r > 100 and g > 100 and b > 80 and abs(r - g) < 30:  # Similar R and G values
            return True

        # Check for cells with light backgrounds but colored text (numbered cells)
        # These often have high green/red but lower blue
        if (r > 90 and g > 90) and b < r + 20 and b < g + 20:
            return True

        return False

    def is_cell_flagged(self, rgb_color):
        """Check if a cell is flagged (has a flag on it)"""
        # Convert to regular Python integers to avoid numpy overflow warnings
        r, g, b = int(rgb_color[0]), int(rgb_color[1]), int(rgb_color[2])

        # CRITICAL FIX: Distinguish between red numbers (3, 7, 8) and flag colors

        # Red numbers have VERY pure red colors with minimal green/blue
        # Number 3: RGB(167, 7, 3) - very low green and blue
        # Number 7: RGB(174, 8, 8) - very low green and blue
        # Number 8: RGB(168, 0, 20) - very low green, some blue
        if r > 120 and g < 30 and b < 30:
            # This is likely a red number, NOT a flag
            return False

        # Flag colors have red but with significant green/blue components
        # RGB(247, 73, 79) - Bright red flag with high green and blue
        # RGB(195, 17, 24) - Dark red flag with some green and blue

        # Flags must have red > 150 AND significant green OR blue (not both low)
        return (r > 150 and r > g + 30 and r > b + 30 and
                (g > 30 or b > 30))  # Flags have substantial green OR blue, numbers don't

    def get_cell_number(self, rgb_color):
        """Try to determine the number on a revealed cell based on color"""
        # Convert to regular Python integers to avoid numpy overflow warnings
        r, g, b = int(rgb_color[0]), int(rgb_color[1]), int(rgb_color[2])

        # Skip very light colors (likely background)
        if r > 200 and g > 200 and b > 200:
            return None

        # Skip very dark colors (likely grid lines)
        if r < 10 and g < 10 and b < 10:
            return None

        # EXPANDED NUMBER DETECTION with more permissive ranges

        # CRITICAL FIX: The problem is "1" detection is too broad and catching "2" colors!
        # Looking at the debug: RGB(79, 98, 203) was detected as "1" but it's actually "2"
        # This suggests the "2" (green) detection is failing and "1" (blue) is catching it

        # 2 = Green: Check GREEN first with more specific detection
        # Original: RGB(35, 107, 0), RGB(21, 99, 0) - GREEN is dominant
        # The key is that "2" has GREEN > BLUE and GREEN > RED significantly
        # IMPROVED: More permissive detection for green "2"
        if (g > 70 and g > r + 15 and g > b + 15) or \
           (g > 90 and g > r + 10 and g > b + 10):  # Two criteria for better coverage
            return 2

        # 4 = Navy Blue: VERY DARK blue detection - CHECK FIRST
        # Original: RGB(1, 2, 132) - extremely low R,G values
        # Key: "4" has R,G < 20 (very dark), "1" has R,G > 50
        if b > 120 and r <= 20 and g <= 20 and b > r + 100 and b > g + 100:  # Very strict for navy
            return 4

        # 1 = Blue: BRIGHT blue detection - CHECK AFTER 4
        # Original: RGB(64, 80, 190), RGB(90, 106, 198) - has moderate R,G values
        # Key: "1" has BLUE > 180 and moderate R,G values (50-120)
        # FIXED: More precise detection to avoid false positives
        if b > 180 and 50 <= r <= 120 and 70 <= g <= 150 and b > r + 60 and b > g + 30:
            return 1

        # 5 = Maroon: Dark red detection - CHECK FIRST before other reds
        # Original: RGB(121, 0, 0) - pure red with NO green or blue
        if 80 < r < 140 and g < 10 and b < 10 and r > g + 70:  # Very specific for maroon
            return 5

        # 3 = Red: Brighter red detection - CHECK AFTER 5
        # Original: RGB(167, 7, 3) - brighter red than 5
        if r > 140 and g < 30 and b < 30 and r > g + 110:  # Higher red threshold than 5
            return 3

        # 6 = Teal: Cyan/teal detection
        # Original: RGB(31, 133, 140)
        if g > 100 and b > 100 and r < 80 and g > r + 20 and b > r + 20:  # Relaxed thresholds
            return 6

        # 7 = Red: Another red variant
        # Original: RGB(174, 8, 8)
        if r > 140 and g < 30 and b < 30 and r > g + 110:  # Relaxed thresholds
            return 7

        # 8 = Red with blue tint: Dark red with slight blue
        # Original: RGB(168, 0, 20)
        if r > 140 and g < 20 and b > 10 and b < 40 and r > g + 120:  # Relaxed thresholds
            return 8

        # If no specific number color detected, return None
        return None

    def detect_game_state(self):
        """Detect the current game state (playing, won, lost)"""
        # Check for popup windows first - this is the most reliable method
        popup_result = self.detect_and_analyze_popup()
        if popup_result:
            if popup_result == "won":
                self.current_game_state = GameState.WON
                return GameState.WON
            elif popup_result == "lost":
                self.current_game_state = GameState.LOST
                return GameState.LOST

        # If no popup, we're still playing
        self.current_game_state = GameState.PLAYING
        return GameState.PLAYING

    def detect_and_analyze_popup(self):
        """Detect popup and determine if it's win or loss - focused on game region"""
        try:
            if not self.board_region:
                print("❌ No board region set - cannot detect popups")
                return None

            # Get the game region with some padding for popups
            board_x, board_y, board_w, board_h = self.board_region

            # Expand the search area around the game board for popups
            # Popups typically appear centered over the game
            padding = 200  # Increased padding to catch more popups
            search_x = max(0, board_x - padding)
            search_y = max(0, board_y - padding)
            search_w = board_w + (2 * padding)
            search_h = board_h + (2 * padding)

            # Take screenshot and crop to search area
            screenshot = self.get_all_monitors_screenshot()

            # Ensure we don't go out of bounds
            max_x = min(search_x + search_w, screenshot.size[0])
            max_y = min(search_y + search_h, screenshot.size[1])

            # Crop to the search region
            search_region = screenshot.crop((search_x, search_y, max_x, max_y))
            search_np = np.array(search_region)

            # Enhanced popup detection approach
            gray = cv2.cvtColor(search_np, cv2.COLOR_RGB2GRAY)

            # Method 1: Look for light colored rectangular regions (typical popup backgrounds)
            bright_mask = gray > 200  # Very bright pixels

            # Method 2: Edge detection with lower thresholds
            edges = cv2.Canny(gray, 30, 100)  # Lower thresholds for better detection

            # Find contours in both bright regions and edges
            bright_contours, _ = cv2.findContours(bright_mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            edge_contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            all_contours = list(bright_contours) + list(edge_contours)

            candidates = []

            for contour in all_contours:
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h

                # Popup size detection - adjusted for actual popup sizes (600x355 = ~213k area)
                if 10000 < area < 300000 and 200 < w < 800 and 150 < h < 500:  # Realistic popup size range
                    aspect_ratio = w / h
                    # Popup dialogs have specific aspect ratios
                    if 0.8 < aspect_ratio < 2.5:  # Covers win ~1.1:1 and loss ~1.7:1
                        # Additional check: make sure it's not the game board by checking if it's much smaller than board
                        board_x, board_y, board_w, board_h = self.board_region
                        board_area = board_w * board_h

                        # Popup should be smaller than the game board (but not too restrictive)
                        if area < board_area * 0.6:  # Popup should be less than 60% of board area
                            candidates.append({
                                'bounds': (x, y, w, h),
                                'area': area,
                                'aspect_ratio': aspect_ratio
                            })

            if not candidates:
                # Try alternative detection method
                return self.detect_popup_alternative_method(search_np)

            # Sort by area (largest first) and test candidates
            candidates.sort(key=lambda c: c['area'], reverse=True)

            for i, candidate in enumerate(candidates[:5]):  # Test top 5 candidates
                x, y, w, h = candidate['bounds']

                # Extract the popup region
                popup_region = search_np[y:y+h, x:x+w]

                # Check if this looks like a popup
                avg_color = np.mean(popup_region, axis=(0, 1))
                color_std = np.std(popup_region, axis=(0, 1))

                # Check multiple criteria for popup detection
                is_light = avg_color[0] > 120 and avg_color[1] > 120 and avg_color[2] > 120
                is_uniform = np.mean(color_std) < 60  # Relatively uniform color
                is_different_from_game = abs(avg_color[0] - 192) < 30  # Close to typical popup gray

                if is_light or is_uniform or is_different_from_game:
                    # Analyze popup content
                    result = self.analyze_popup_content(popup_region)
                    if result:
                        return result

            return None

        except Exception as e:
            print(f"❌ Error detecting popup: {e}")
            return None

    def detect_popup_alternative_method(self, search_np):
        """Alternative popup detection method using color analysis"""
        try:
            # Convert to different color spaces for analysis
            gray = cv2.cvtColor(search_np, cv2.COLOR_RGB2GRAY)
            hsv = cv2.cvtColor(search_np, cv2.COLOR_RGB2HSV)

            # Look for regions with low saturation (typical of popup dialogs)
            low_saturation = hsv[:, :, 1] < 50  # Low saturation
            high_value = hsv[:, :, 2] > 150     # High brightness

            # Combine conditions for popup-like regions
            popup_mask = low_saturation & high_value

            # Find contours in the mask
            contours, _ = cv2.findContours(popup_mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for i, contour in enumerate(contours):
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h

                # Apply same criteria as main detection
                if 10000 < area < 300000 and 200 < w < 800 and 150 < h < 500:
                    aspect_ratio = w / h
                    if 0.8 < aspect_ratio < 2.5:
                        # Check against board size
                        board_x, board_y, board_w, board_h = self.board_region
                        board_area = board_w * board_h

                        if area < board_area * 0.6:
                            # Extract and analyze this region
                            popup_region = search_np[y:y+h, x:x+w]

                            # Analyze content
                            result = self.analyze_popup_content(popup_region)
                            if result:
                                return result

            return None

        except Exception as e:
            return None

    def analyze_popup_content(self, popup_region):
        """Analyze popup content to determine win/loss"""
        try:
            # Convert to grayscale for text analysis
            gray_popup = cv2.cvtColor(popup_region, cv2.COLOR_RGB2GRAY)
            h, w = gray_popup.shape

            # PRIMARY METHOD: Size ratio analysis (most reliable for Windows Minesweeper)
            aspect_ratio = w / h if h > 0 else 0

            # Based on observation: LOSS ~1.7:1 (wider), WIN ~1.1:1 (more square)
            ratio_win_score = 0
            ratio_loss_score = 0

            if aspect_ratio > 1.5:
                ratio_loss_score += 3  # Wide popup suggests LOSS dialog
            elif aspect_ratio > 1.4:
                ratio_loss_score += 2  # Moderately wide suggests LOSS
            elif aspect_ratio > 1.3:
                ratio_loss_score += 1  # Somewhat wide
            elif aspect_ratio < 1.2:
                ratio_win_score += 3  # Very square suggests WIN dialog
            elif aspect_ratio < 1.3:
                ratio_win_score += 2  # Square suggests WIN

            # Simple decision logic based ONLY on aspect ratio
            if ratio_win_score > 0:
                return "won"
            elif ratio_loss_score > 0:
                return "lost"
            else:
                return "lost"  # Default to loss for safety

        except Exception as e:
            return "lost"  # Default to loss for safety

    def detect_loss_state(self):
        """Detect if the game is lost by looking for exploded mines (red cells)"""
        if not self.board_region:
            return False

        try:
            # Capture the board area
            board_np = self.analyze_board_state()
            if board_np is None:
                return False

            # If we have cell area bounds, focus on just the cell grid
            if hasattr(self, 'cell_area_bounds'):
                cell_x, cell_y, cell_w, cell_h = self.cell_area_bounds
                board_x, board_y, board_w, board_h = self.board_region

                # Extract just the cell area from the board image
                rel_x = cell_x - board_x
                rel_y = cell_y - board_y

                if rel_x >= 0 and rel_y >= 0 and rel_x + cell_w <= board_w and rel_y + cell_h <= board_h:
                    cell_area = board_np[rel_y:rel_y+cell_h, rel_x:rel_x+cell_w]
                    print(f"🔍 Analyzing cell area: {cell_area.shape}")
                else:
                    cell_area = board_np
                    print(f"🔍 Using full board area: {cell_area.shape}")
            else:
                cell_area = board_np
                print(f"🔍 Using full board area: {cell_area.shape}")

            # Convert to HSV for better red detection
            hsv = cv2.cvtColor(cell_area, cv2.COLOR_RGB2HSV)

            # Multiple red ranges to catch different explosion colors
            red_ranges = [
                # Standard red ranges
                ([0, 120, 70], [10, 255, 255], "Bright red"),
                ([170, 120, 70], [180, 255, 255], "Deep red"),
                # More permissive ranges for different themes
                ([0, 80, 50], [15, 255, 255], "Light red"),
                ([165, 80, 50], [180, 255, 255], "Dark red"),
                # Very broad range
                ([0, 50, 30], [20, 255, 255], "Very broad red 1"),
                ([160, 50, 30], [180, 255, 255], "Very broad red 2"),
            ]

            total_red_pixels = 0

            for i, (lower, upper, desc) in enumerate(red_ranges):
                lower_red = np.array(lower)
                upper_red = np.array(upper)
                red_mask = cv2.inRange(hsv, lower_red, upper_red)

                red_pixels = np.sum(red_mask > 0)
                total_red_pixels += red_pixels

                if red_pixels > 0:
                    print(f"🔴 {desc}: {red_pixels} pixels")
                    # Save mask for debugging
                    cv2.imwrite(f"red_mask_{i}_{desc.replace(' ', '_')}.png", red_mask)

            total_pixels = cell_area.shape[0] * cell_area.shape[1]
            red_percentage = (total_red_pixels / total_pixels) * 100

            print(f"🔴 Total red pixels: {total_red_pixels} ({red_percentage:.2f}%)")

            # Only consider it a loss if we have significant red pixels AND some moves have been made
            # This prevents false positives on fresh boards
            moves_made = len(self.revealed_cells)

            if red_percentage > 0.5 and moves_made > 0:  # Higher threshold and require moves
                print(f"💥 Loss detected - red pixels found after {moves_made} moves!")
                return True
            elif red_percentage > 0.1:
                print(f"🔴 Red pixels detected ({red_percentage:.2f}%) but threshold not met or no moves made yet")

            # Also check for dark/black areas that might indicate exploded mines
            # But only if we've made some moves
            if moves_made > 0:
                gray = cv2.cvtColor(cell_area, cv2.COLOR_RGB2GRAY)
                dark_pixels = np.sum(gray < 30)  # Very dark pixels (stricter threshold)
                dark_percentage = (dark_pixels / total_pixels) * 100

                print(f"⚫ Dark pixels: {dark_pixels} ({dark_percentage:.2f}%)")

                if dark_percentage > 10:  # Higher threshold for dark areas
                    print(f"💥 Loss detected - significant dark areas found after {moves_made} moves!")
                    return True

            print(f"✅ No loss detected (moves made: {moves_made})")

        except Exception as e:
            print(f"❌ Error detecting loss state: {e}")

        return False

    def detect_win_state(self):
        """Detect if the game is won by checking if all non-mine cells are revealed"""
        if not self.board_region:
            return False

        try:
            # Capture the board area
            board_np = self.analyze_board_state()
            if board_np is None:
                return False

            # Convert to HSV
            hsv = cv2.cvtColor(board_np, cv2.COLOR_RGB2HSV)

            # Look for blue (unrevealed) cells
            lower_blue = np.array([100, 50, 100])
            upper_blue = np.array([130, 255, 255])
            blue_mask = cv2.inRange(hsv, lower_blue, upper_blue)

            # Count blue pixels
            blue_pixels = np.sum(blue_mask > 0)
            total_pixels = blue_mask.shape[0] * blue_mask.shape[1]
            blue_percentage = (blue_pixels / total_pixels) * 100

            print(f"🔵 Blue (unrevealed) pixels: {blue_pixels} ({blue_percentage:.2f}%)")

            # If very few blue pixels remain, likely won
            # Account for flags which might still be blue-ish
            if blue_percentage < 15:  # Less than 15% blue means mostly revealed
                return True

        except Exception as e:
            print(f"❌ Error detecting win state: {e}")

        return False

    def detect_popup_window(self):
        """Detect if there's a game over popup window - focused on game region"""
        try:
            if not self.board_region:
                print("❌ No board region set - cannot detect popups")
                return False

            # Get the game region with padding for popups
            board_x, board_y, board_w, board_h = self.board_region

            # Expand search area around the game board
            padding = 100
            search_x = max(0, board_x - padding)
            search_y = max(0, board_y - padding)
            search_w = board_w + (2 * padding)
            search_h = board_h + (2 * padding)

            # Take screenshot and crop to search area
            screenshot = self.get_all_monitors_screenshot()

            # Ensure bounds
            max_x = min(search_x + search_w, screenshot.size[0])
            max_y = min(search_y + search_h, screenshot.size[1])

            # Crop to search region
            search_region = screenshot.crop((search_x, search_y, max_x, max_y))
            search_np = np.array(search_region)

            # Look for popup-like rectangular regions
            gray = cv2.cvtColor(search_np, cv2.COLOR_RGB2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h

                # Look for popup-sized rectangular regions
                if 5000 < area < 50000 and 100 < w < 400 and 50 < h < 200:
                    aspect_ratio = w / h
                    if 1.5 < aspect_ratio < 4.0:  # Typical popup aspect ratio
                        print(f"📋 Popup detected: {w}x{h} at ({x},{y}) in game region")

                        # Sample colors to confirm it's a popup
                        region = search_np[y:y+h, x:x+w]
                        avg_color = np.mean(region, axis=(0, 1))

                        # Popups have light backgrounds
                        if avg_color[0] > 150 and avg_color[1] > 150 and avg_color[2] > 150:
                            print(f"✅ Popup confirmed - light background detected")
                            return True

        except Exception as e:
            print(f"❌ Error detecting popup: {e}")

        return False

    def handle_loss_popup(self):
        """Handle the loss popup by clicking 'Play Again' button using relative positioning"""
        print("🔄 Handling loss popup - looking for 'Play Again' button...")

        try:
            # Use relative positioning based on game board coordinates
            button_positions = self.find_popup_button_relative_to_board()

            if button_positions:
                print(f"🎯 Found {len(button_positions)} button candidates using relative positioning")

                for i, (mouse_x, mouse_y) in enumerate(button_positions):
                    print(f"🎯 Trying button position {i+1}/{len(button_positions)}: mouse({mouse_x}, {mouse_y})")

                    # Move mouse to position and pause to see where we're clicking
                    pyautogui.moveTo(mouse_x, mouse_y, duration=0.2)
                    time.sleep(0.5)  # Longer pause to see the mouse position

                    # Click and wait
                    pyautogui.click(mouse_x, mouse_y)
                    time.sleep(1.5)  # Longer wait to see if popup closes

                    # Check if popup is gone using the same detection method
                    if not self.is_popup_still_visible():
                        print(f"✅ Successfully clicked 'Play Again' button at position {i+1}!")
                        self.reset_game_state()
                        return True
                    else:
                        print(f"❌ Position {i+1} didn't work, popup still visible")

                print("❌ None of the calculated button positions worked")
            else:
                print("❌ Could not find popup or calculate relative positions")

            # If popup-based clicking didn't work, try keyboard shortcuts
            print("🔄 Trying keyboard shortcuts...")

            # Try Enter key
            pyautogui.press('enter')
            time.sleep(1)
            if not self.is_popup_still_visible():
                print("✅ Popup closed with Enter key!")
                self.reset_game_state()
                return True

            # Try Space key
            pyautogui.press('space')
            time.sleep(1)
            if not self.is_popup_still_visible():
                print("✅ Popup closed with Space key!")
                self.reset_game_state()
                return True

            # Try Tab + Enter (in case we need to focus the button)
            pyautogui.press('tab')
            time.sleep(0.5)
            pyautogui.press('enter')
            time.sleep(1)
            if not self.is_popup_still_visible():
                print("✅ Popup closed with Tab+Enter!")
                self.reset_game_state()
                return True

        except Exception as e:
            print(f"❌ Error handling popup: {e}")

        print("❌ Could not close popup automatically")
        return False

    def find_popup_bounds(self):
        """Find the actual bounds of the popup window"""
        try:
            if not self.board_region:
                return None

            # Use the same detection logic as detect_and_analyze_popup but return bounds
            board_x, board_y, board_w, board_h = self.board_region
            padding = 200
            search_x = max(0, board_x - padding)
            search_y = max(0, board_y - padding)
            search_w = board_w + (2 * padding)
            search_h = board_h + (2 * padding)

            screenshot = self.get_all_monitors_screenshot()
            max_x = min(search_x + search_w, screenshot.size[0])
            max_y = min(search_y + search_h, screenshot.size[1])
            search_region = screenshot.crop((search_x, search_y, max_x, max_y))
            search_np = np.array(search_region)

            # Use same detection methods
            gray = cv2.cvtColor(search_np, cv2.COLOR_RGB2GRAY)
            bright_mask = gray > 200
            edges = cv2.Canny(gray, 30, 100)
            bright_contours, _ = cv2.findContours(bright_mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            edge_contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            all_contours = list(bright_contours) + list(edge_contours)

            candidates = []
            for contour in all_contours:
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h

                if 10000 < area < 300000 and 200 < w < 800 and 150 < h < 500:
                    aspect_ratio = w / h
                    if 0.8 < aspect_ratio < 2.5:
                        board_area = board_w * board_h
                        if area < board_area * 0.6:
                            candidates.append({
                                'bounds': (x, y, w, h),
                                'area': area,
                                'aspect_ratio': aspect_ratio
                            })

            if candidates:
                # Sort by area and return the largest valid popup
                candidates.sort(key=lambda c: c['area'], reverse=True)
                for candidate in candidates:
                    x, y, w, h = candidate['bounds']
                    popup_region = search_np[y:y+h, x:x+w]
                    avg_color = np.mean(popup_region, axis=(0, 1))
                    color_std = np.std(popup_region, axis=(0, 1))

                    is_light = avg_color[0] > 120 and avg_color[1] > 120 and avg_color[2] > 120
                    is_uniform = np.mean(color_std) < 60
                    is_different_from_game = abs(avg_color[0] - 192) < 30

                    if is_light or is_uniform or is_different_from_game:
                        # Convert back to screen coordinates
                        screen_x = search_x + x
                        screen_y = search_y + y
                        return (screen_x, screen_y, w, h)

            return None

        except Exception as e:
            print(f"❌ Error finding popup bounds: {e}")
            return None

    def find_buttons_in_popup(self, popup_bounds):
        """Find actual button regions within the popup"""
        try:
            popup_x, popup_y, popup_w, popup_h = popup_bounds

            # Take a screenshot of just the popup area
            screenshot = self.get_all_monitors_screenshot()
            popup_region = screenshot.crop((popup_x, popup_y, popup_x + popup_w, popup_y + popup_h))
            popup_np = np.array(popup_region)

            # Convert to grayscale for button detection
            gray = cv2.cvtColor(popup_np, cv2.COLOR_RGB2GRAY)

            # Look for button-like rectangular regions
            # Buttons typically have edges/borders
            edges = cv2.Canny(gray, 50, 150)

            # Use morphological operations to find rectangular button shapes
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

            # Find contours that could be buttons
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            button_candidates = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h

                # Look for button-sized rectangles
                # Buttons are typically 60-150 pixels wide, 20-40 pixels tall
                if 1000 < area < 8000 and 60 < w < 200 and 20 < h < 50:
                    aspect_ratio = w / h
                    # Buttons are wider than tall
                    if 1.5 < aspect_ratio < 8.0:
                        # Convert back to screen coordinates
                        screen_x = popup_x + x
                        screen_y = popup_y + y
                        button_candidates.append((screen_x, screen_y, w, h))
                        print(f"🔘 Found button candidate: {w}x{h} at ({screen_x}, {screen_y})")

            # Sort by area (larger buttons first)
            button_candidates.sort(key=lambda b: b[2] * b[3], reverse=True)

            return button_candidates[:3]  # Return top 3 button candidates

        except Exception as e:
            print(f"❌ Error finding buttons in popup: {e}")
            return []

    def find_popup_button_relative_to_board(self):
        """Find popup button positions relative to game board and translate to mouse coordinates"""
        try:
            if not self.board_region:
                print("❌ No board region available for relative positioning")
                return []

            # Get current screenshot and find popup
            popup_bounds = self.find_popup_bounds()
            if not popup_bounds:
                print("❌ Could not find popup bounds for relative positioning")
                return []

            popup_x, popup_y, popup_w, popup_h = popup_bounds
            board_x, board_y, board_w, board_h = self.board_region

            print(f"📊 Board region: ({board_x}, {board_y}) size {board_w}x{board_h}")
            print(f"📊 Popup region: ({popup_x}, {popup_y}) size {popup_w}x{popup_h}")

            # Calculate popup position relative to board edges
            # These are in screenshot pixel coordinates
            popup_center_x = popup_x + popup_w // 2
            popup_center_y = popup_y + popup_h // 2

            # Calculate relative positions as fractions of board dimensions
            rel_x = (popup_center_x - board_x) / board_w  # How far across the board (0.0 = left edge, 1.0 = right edge)
            rel_y = (popup_center_y - board_y) / board_h  # How far down the board (0.0 = top edge, 1.0 = bottom edge)

            print(f"📍 Popup center relative to board: ({rel_x:.3f}, {rel_y:.3f})")

            # Generate button candidates relative to popup center
            # These are offsets in screenshot pixels
            button_offsets = []
            for y_offset in [40, 60, 80, 100]:  # Distance from popup center toward bottom
                for x_offset in [-80, -40, 0, 40, 80]:  # Left to right across popup
                    button_offsets.append((x_offset, y_offset))

            # Convert to mouse coordinates using calibration data
            mouse_positions = []

            if hasattr(self, 'calibration_data') and self.calibration_data:
                print("🎯 Using calibration data for coordinate translation")

                for x_offset, y_offset in button_offsets:
                    # Calculate button position in screenshot coordinates
                    button_screenshot_x = popup_center_x + x_offset
                    button_screenshot_y = popup_center_y + y_offset

                    # Convert to relative position within board
                    button_rel_x = (button_screenshot_x - board_x) / board_w
                    button_rel_y = (button_screenshot_y - board_y) / board_h

                    # Skip if button is outside reasonable bounds
                    if not (-0.5 <= button_rel_x <= 1.5 and -0.5 <= button_rel_y <= 1.5):
                        continue

                    # Use calibration to convert to mouse coordinates
                    mouse_x, mouse_y = self.convert_relative_to_mouse_coords(button_rel_x, button_rel_y)

                    if mouse_x is not None and mouse_y is not None:
                        mouse_positions.append((mouse_x, mouse_y))
                        print(f"🎯 Button candidate: screenshot({button_screenshot_x}, {button_screenshot_y}) -> relative({button_rel_x:.3f}, {button_rel_y:.3f}) -> mouse({mouse_x}, {mouse_y})")
            else:
                print("❌ No calibration data available - cannot translate coordinates")
                return []

            return mouse_positions

        except Exception as e:
            print(f"❌ Error finding popup buttons relative to board: {e}")
            return []

    def convert_relative_to_mouse_coords(self, rel_x, rel_y):
        """Convert relative board position to actual mouse coordinates using calibration"""
        try:
            if not hasattr(self, 'calibration_data') or not self.calibration_data:
                return None, None

            # Get board corners from calibration
            top_left = self.calibration_data.get('top_left')
            top_right = self.calibration_data.get('top_right')
            bottom_left = self.calibration_data.get('bottom_left')
            bottom_right = self.calibration_data.get('bottom_right')

            if not all([top_left, top_right, bottom_left, bottom_right]):
                print("❌ Incomplete calibration data")
                return None, None

            # Bilinear interpolation to find mouse coordinates
            # First interpolate along top and bottom edges
            top_x = top_left[0] + rel_x * (top_right[0] - top_left[0])
            top_y = top_left[1] + rel_x * (top_right[1] - top_left[1])

            bottom_x = bottom_left[0] + rel_x * (bottom_right[0] - bottom_left[0])
            bottom_y = bottom_left[1] + rel_x * (bottom_right[1] - bottom_left[1])

            # Then interpolate between top and bottom
            mouse_x = top_x + rel_y * (bottom_x - top_x)
            mouse_y = top_y + rel_y * (bottom_y - top_y)

            return int(mouse_x), int(mouse_y)

        except Exception as e:
            print(f"❌ Error converting relative coordinates: {e}")
            return None, None

    def is_popup_still_visible(self):
        """Check if popup is still visible using the same detection logic as detect_and_analyze_popup"""
        try:
            # Use the same detection logic but just return True/False
            popup_result = self.detect_and_analyze_popup()
            return popup_result is not None
        except Exception as e:
            print(f"❌ Error checking popup visibility: {e}")
            return False  # Assume popup is gone if we can't detect it

    def reset_game_state(self):
        """Reset game state after popup is closed"""
        self.revealed_cells.clear()
        self.flagged_cells.clear()
        self.current_game_state = GameState.PLAYING

    def is_cell_actually_unrevealed(self, row, col, debug=False, board_np=None):
        """Check if a cell is actually unrevealed by examining its visual appearance"""
        # First check our tracking - if we flagged it, it's not unrevealed
        if (row, col) in self.flagged_cells:
            if debug:
                print(f"🔍 Cell ({row}, {col}): In flagged_cells tracking - not unrevealed")
            return False

        if (row, col) in self.revealed_cells:
            if debug:
                print(f"🔍 Cell ({row}, {col}): In revealed_cells tracking - not unrevealed")
            return False

        try:
            cell_info = self.get_cell_color_info(row, col, board_np)
            if cell_info is None:
                if debug:
                    print(f"🔍 Cell ({row}, {col}): No color info available, using tracking")
                # If we can't get color info, fall back to our tracking
                return (row, col) not in self.revealed_cells and (row, col) not in self.flagged_cells

            # Use visual detection to determine if cell is unrevealed
            is_unrevealed = not cell_info['is_revealed'] and not cell_info['is_flagged']

            if debug:
                print(f"🔍 Cell ({row}, {col}): RGB={cell_info['rgb']}, revealed={cell_info['is_revealed']}, flagged={cell_info['is_flagged']}, unrevealed={is_unrevealed}")

            return is_unrevealed
        except Exception as e:
            print(f"⚠️ Error checking cell ({row}, {col}) state: {e}")
            # Fall back to our tracking if visual detection fails
            return (row, col) not in self.revealed_cells and (row, col) not in self.flagged_cells

    def close_popup(self):
        """General method to close any popup"""
        return self.handle_loss_popup()

    def test_cell_detection(self, num_cells=5):
        """Test cell detection on a few random cells for debugging"""
        print(f"\n🔍 TESTING CELL DETECTION ON {num_cells} RANDOM CELLS")
        print("=" * 70)

        board_np = self.analyze_board_state()
        if board_np is None:
            print("❌ Could not capture board state")
            return

        import random
        tested_cells = 0
        for _ in range(num_cells * 3):  # Try more cells to find valid ones
            if tested_cells >= num_cells:
                break

            row = random.randint(0, self.board_height - 1)
            col = random.randint(0, self.board_width - 1)

            cell_info = self.get_cell_color_info(row, col, board_np)
            if cell_info:
                votes = cell_info['votes']
                print(f"Cell ({row:2d}, {col:2d}): Revealed={cell_info['is_revealed']} | Flagged={cell_info['is_flagged']}")
                print(f"  Votes: {votes['revealed']}/{votes['total']} revealed, {votes['flagged']}/{votes['total']} flagged")
                if 'edge_sample_colors' in cell_info:
                    print(f"  Edge sample colors: {cell_info['edge_sample_colors'][:3]}...")  # Show first 3 colors
                tested_cells += 1

        print("=" * 70)

    def test_specific_cell(self, row, col):
        """Test detection on a specific cell for debugging"""
        print(f"\n🔍 TESTING SPECIFIC CELL ({row}, {col})")
        print("=" * 50)

        board_np = self.analyze_board_state()
        if board_np is None:
            print("❌ Could not capture board state")
            return

        cell_info = self.get_cell_color_info(row, col, board_np)
        if cell_info:
            votes = cell_info['votes']
            print(f"Cell ({row}, {col}):")
            print(f"  Final Result: Revealed={cell_info['is_revealed']} | Flagged={cell_info['is_flagged']} | Number={cell_info['number']}")
            print(f"  Votes: {votes['revealed']}/{votes['total']} revealed, {votes['flagged']}/{votes['total']} flagged")
            if 'number_votes' in cell_info and cell_info['number_votes']:
                print(f"  Number votes: {cell_info['number_votes']}")
            if 'edge_sample_colors' in cell_info:
                print(f"  Edge sample colors:")
                for i, color in enumerate(cell_info['edge_sample_colors']):
                    r, g, b = color
                    is_revealed = self.is_cell_revealed(color)
                    print(f"    Edge {i+1}: RGB{color} -> Revealed={is_revealed}")

            if 'center_sample_colors' in cell_info:
                print(f"  Center sample colors:")
                for i, color in enumerate(cell_info['center_sample_colors']):
                    r, g, b = color
                    number = self.get_cell_number(color)
                    print(f"    Center {i+1}: RGB{color} -> Number={number}")
        else:
            print("❌ Could not get cell info")

        print("=" * 50)

    def test_number_detection(self):
        """Test number detection on revealed cells"""
        print(f"\n🔢 TESTING NUMBER DETECTION")
        print("=" * 50)

        board_np = self.analyze_board_state()
        if board_np is None:
            print("❌ Could not capture board state")
            return

        numbers_found = {}
        for row in range(self.board_height):
            for col in range(self.board_width):
                cell_info = self.get_cell_color_info(row, col, board_np)
                if cell_info and cell_info['is_revealed'] and cell_info['number'] is not None:
                    number = cell_info['number']
                    if number not in numbers_found:
                        numbers_found[number] = []
                    numbers_found[number].append((row, col))

        print("Numbers detected on the board:")
        for number in sorted(numbers_found.keys()):
            cells = numbers_found[number]
            print(f"  {number}: {len(cells)} cells - {cells[:5]}{'...' if len(cells) > 5 else ''}")

        if not numbers_found:
            print("  No numbers detected - board might be mostly unrevealed")

        print("=" * 50)

    def debug_color_detection(self, row, col):
        """Debug color detection for a specific cell - shows all RGB values and detection results"""
        print(f"\n🎨 DEBUG COLOR DETECTION FOR CELL ({row}, {col})")
        print("=" * 60)

        board_np = self.analyze_board_state()
        if board_np is None:
            print("❌ Could not capture board state")
            return

        cell_info = self.get_cell_color_info(row, col, board_np)
        if cell_info:
            print(f"Final Detection: Revealed={cell_info['is_revealed']}, Number={cell_info['number']}")
            print(f"Sample Analysis:")

            for i, color in enumerate(cell_info['sample_colors']):
                r, g, b = color
                is_revealed = self.is_cell_revealed(color)
                number = self.get_cell_number(color)
                print(f"  Sample {i+1}: RGB({r:3d}, {g:3d}, {b:3d}) -> Revealed={is_revealed}, Number={number}")

            if 'number_votes' in cell_info and cell_info['number_votes']:
                print(f"Number Votes: {cell_info['number_votes']}")
            else:
                print("Number Votes: None detected")
        else:
            print("❌ Could not get cell info")

        print("=" * 60)

    def discover_number_colors_precise(self):
        """More precise color discovery - samples the center of cells to find actual number colors"""
        print(f"\n🎨 PRECISE NUMBER COLOR DISCOVERY")
        print("=" * 60)
        print("This will sample the CENTER of numbered cells to find the actual number colors.")
        print("We'll look for the colored text, not the background.")
        print()

        board_np = self.analyze_board_state()
        if board_np is None:
            print("❌ Could not capture board state")
            return

        discovered_colors = {}

        for number in range(1, 9):
            print(f"\n🔢 Looking for number {number}")
            coords_input = input(f"Enter coordinates of a cell with number {number} (row,col) or 'skip': ")

            if coords_input.lower() == 'skip':
                continue

            try:
                row, col = map(int, coords_input.split(','))

                # Sample the CENTER of the cell more precisely
                cell_left = col * self.cell_width
                cell_top = row * self.cell_height
                cell_center_x = cell_left + self.cell_width / 2
                cell_center_y = cell_top + self.cell_height / 2

                # Sample a small area around the center to catch the number
                sample_points = [
                    (cell_center_x, cell_center_y),  # Dead center
                    (cell_center_x - 2, cell_center_y),  # Slightly left
                    (cell_center_x + 2, cell_center_y),  # Slightly right
                    (cell_center_x, cell_center_y - 2),  # Slightly up
                    (cell_center_x, cell_center_y + 2),  # Slightly down
                ]

                print(f"Cell ({row}, {col}) center area colors:")
                colors_found = []

                for i, (x, y) in enumerate(sample_points):
                    if 0 <= int(y) < board_np.shape[0] and 0 <= int(x) < board_np.shape[1]:
                        color = board_np[int(y), int(x)]
                        r, g, b = int(color[0]), int(color[1]), int(color[2])
                        print(f"  Point {i+1}: RGB({r:3d}, {g:3d}, {b:3d})")
                        colors_found.append((r, g, b))

                # Find colors that are NOT light background (likely the number)
                number_colors = []
                for r, g, b in colors_found:
                    # Skip light background colors
                    if not (r > 200 and g > 200 and b > 200):
                        number_colors.append((r, g, b))

                if number_colors:
                    print(f"\n🎯 Likely number colors (non-background):")
                    unique_number_colors = list(set(number_colors))
                    for i, (r, g, b) in enumerate(unique_number_colors):
                        print(f"  Number color {i+1}: RGB({r:3d}, {g:3d}, {b:3d})")

                    discovered_colors[number] = unique_number_colors
                else:
                    print("❌ No distinct number colors found - might be empty cell or sampling issue")

            except ValueError:
                print("❌ Invalid format. Use: row,col (e.g., 5,10)")
            except Exception as e:
                print(f"❌ Error analyzing cell: {e}")

        print(f"\n📊 DISCOVERED NUMBER COLORS:")
        print("=" * 60)
        for number, colors in discovered_colors.items():
            print(f"Number {number}:")
            for r, g, b in colors:
                print(f"  RGB({r:3d}, {g:3d}, {b:3d})")

        print("\n🎨 Based on your description:")
        print("1 = Blue, 2 = Green, 3 = Red, 4 = Navy Blue, 5 = Maroon, 6 = Teal")
        print("Let's see if the discovered colors match this pattern!")
        print("=" * 60)

    def debug_number_detection(self, row, col):
        """Debug number detection for a specific cell with detailed color analysis"""
        print(f"\n🔍 DETAILED NUMBER DETECTION DEBUG FOR CELL ({row}, {col})")
        print("=" * 60)

        board_np = self.analyze_board_state()
        if board_np is None:
            print("❌ Could not capture board state")
            return

        cell_info = self.get_cell_color_info(row, col, board_np)
        if cell_info:
            print(f"Cell ({row}, {col}) analysis:")
            print(f"  Final Result: Revealed={cell_info['is_revealed']}, Number={cell_info['number']}")

            if 'number_votes' in cell_info:
                print(f"  Number votes: {cell_info['number_votes']}")

            print(f"  All sample colors and their number detection:")
            for i, color in enumerate(cell_info['sample_colors']):
                r, g, b = color
                detected_number = self.get_cell_number(color)
                print(f"    Sample {i+1}: RGB({r:3d}, {g:3d}, {b:3d}) -> Number={detected_number}")

                # Test each number detection condition
                print(f"      Testing number conditions:")
                if b > 150 and b > r + 50 and b > g + 50:
                    if 50 <= r <= 100 and 70 <= g <= 100 and 180 <= b <= 200:
                        print(f"        ✅ Matches number 1 (blue)")
                if g > 150 and g > r + 20 and g > b + 20:
                    if 130 <= r <= 150 and 170 <= g <= 190 and 130 <= b <= 150:
                        print(f"        ✅ Matches number 2 (green)")
                if r > 150 and r > g + 100 and r > b + 100:
                    if 160 <= r <= 200 and g <= 20 and b <= 20:
                        print(f"        ✅ Matches number 3 (red)")
                # Add more conditions as needed...
        else:
            print("❌ Could not get cell info")

        print("=" * 60)

    def test_current_board_detection(self):
        """Test number detection on the current board state"""
        print(f"\n🧪 TESTING CURRENT BOARD DETECTION")
        print("=" * 50)

        board_np = self.analyze_board_state()
        if board_np is None:
            print("❌ Could not capture board state")
            return

        print("Scanning entire board for revealed cells and numbers...")
        revealed_count = 0
        numbered_count = 0

        for row in range(self.board_height):
            for col in range(self.board_width):
                cell_info = self.get_cell_color_info(row, col, board_np)
                if cell_info and cell_info['is_revealed']:
                    revealed_count += 1
                    if cell_info['number'] is not None and cell_info['number'] > 0:
                        numbered_count += 1
                        print(f"  📍 Cell ({row:2d}, {col:2d}): Number {cell_info['number']}")

        print(f"\n📊 Board Summary:")
        print(f"  Revealed cells: {revealed_count}")
        print(f"  Numbered cells: {numbered_count}")
        print(f"  Empty revealed cells: {revealed_count - numbered_count}")

        if numbered_count == 0:
            print("\n⚠️ No numbered cells detected!")
            print("This could mean:")
            print("  - The board is mostly unrevealed")
            print("  - Number detection colors need adjustment")
            print("  - The board capture is not working correctly")

        print("=" * 50)

    def get_cell_number_safe(self, row, col, board_np=None):
        """Get the number on a revealed cell, returns None if not revealed or not a number"""
        try:
            cell_info = self.get_cell_color_info(row, col, board_np)
            if cell_info and cell_info['is_revealed'] and not cell_info['is_flagged']:
                return cell_info['number']
            return None
        except Exception:
            return None

    def get_all_neighbors(self, row, col):
        """Get all neighbor cell coordinates (revealed and unrevealed)"""
        neighbors = []
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0:
                    continue
                nr, nc = row + dr, col + dc
                if 0 <= nr < self.board_height and 0 <= nc < self.board_width:
                    neighbors.append((nr, nc))
        return neighbors

    def get_unrevealed_neighbors(self, row, col, board_np=None):
        """Get unrevealed neighbor cells by checking visual state"""
        neighbors = []

        # Capture board once for all neighbor checks if not provided
        if board_np is None:
            board_np = self.analyze_board_state()

        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0:
                    continue
                nr, nc = row + dr, col + dc
                if 0 <= nr < self.board_height and 0 <= nc < self.board_width:
                    # Check if cell is actually unrevealed by examining its visual state
                    if self.is_cell_actually_unrevealed(nr, nc, board_np=board_np):
                        neighbors.append((nr, nc))
        return neighbors

    def get_flagged_neighbors(self, row, col, board_np=None):
        """Get flagged neighbor cells using tracking + visual detection"""
        neighbors = []

        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0:
                    continue
                nr, nc = row + dr, col + dc
                if 0 <= nr < self.board_height and 0 <= nc < self.board_width:

                    # FIRST: Check our own tracking (most reliable for bot-placed flags)
                    if (nr, nc) in self.flagged_cells:
                        neighbors.append((nr, nc))
                        # Removed verbose flag tracking message for cleaner output
                        continue

                    # Simple flag detection using color detection only
                    if board_np is None:
                        board_np = self.analyze_board_state()

                    cell_info = self.get_cell_color_info(nr, nc, board_np)
                    if cell_info and cell_info['is_flagged']:
                        neighbors.append((nr, nc))
                        # Update our tracking if we discover a manual flag
                        self.flagged_cells.add((nr, nc))
        return neighbors

    def make_safe_moves(self):
        """CLEAN SIMPLE LOGIC: Find and execute safe moves using basic minesweeper rules"""
        print("\n🧠 ANALYZING BOARD FOR SAFE MOVES...")

        # If no cells revealed yet, click the center
        if not self.revealed_cells:
            print("🎮 Starting new game - ensuring Minesweeper window has focus...")
            self.ensure_minesweeper_focus()
            center_row, center_col = self.board_height // 2, self.board_width // 2
            print(f"🎯 Making initial move at center: ({center_row}, {center_col})")
            self.click_cell(center_row, center_col)
            return 1

        # Step 1: Get current board state
        board_np = self.analyze_board_state()
        if board_np is None:
            print("❌ Could not capture board state")
            return 0

        # Step 2: Sync visual state with internal tracking
        self.sync_visual_state(board_np)

        # Step 3: Find all safe moves and mine cells using simple logic
        safe_cells, mine_cells = self.find_logical_moves(board_np)

        # Step 4: Execute the moves
        moves_made = self.execute_moves(safe_cells, mine_cells)

        return moves_made

    def sync_visual_state(self, board_np):
        """Sync internal tracking with visual board state"""
        print("🔄 Syncing visual state with internal tracking...")

        # Scan entire board and update tracking
        new_revealed = []
        new_flagged = []

        for row in range(self.board_height):
            for col in range(self.board_width):
                cell_info = self.get_cell_color_info(row, col, board_np)
                if cell_info:
                    if cell_info['is_revealed']:
                        # Add to revealed_cells if not already there
                        if (row, col) not in self.revealed_cells:
                            self.revealed_cells.add((row, col))
                            new_revealed.append((row, col))
                        # Remove from flagged_cells if it was there (shouldn't happen but safety check)
                        if (row, col) in self.flagged_cells:
                            self.flagged_cells.discard((row, col))
                    elif cell_info['is_flagged']:
                        # Add to flagged_cells if not already there
                        if (row, col) not in self.flagged_cells:
                            self.flagged_cells.add((row, col))
                            new_flagged.append((row, col))
                        # Remove from revealed_cells if it was there (shouldn't happen but safety check)
                        if (row, col) in self.revealed_cells:
                            self.revealed_cells.discard((row, col))

        if new_revealed:
            print(f"🔍 Detected {len(new_revealed)} newly revealed cells: {new_revealed}")
        if new_flagged:
            print(f"🚩 Detected {len(new_flagged)} newly flagged cells: {new_flagged}")

    def find_logical_moves(self, board_np):
        """Find safe cells and mine cells using basic minesweeper logic"""
        safe_cells = []
        mine_cells = []

        print("🔍 Scanning all numbered cells for logical deductions...")

        # Check every revealed numbered cell
        for row in range(self.board_height):
            for col in range(self.board_width):
                cell_number = self.get_cell_number_safe(row, col, board_np)

                # Only process cells with numbers > 0
                if cell_number is not None and isinstance(cell_number, int) and cell_number > 0:
                    unrevealed_neighbors = self.get_unrevealed_neighbors(row, col, board_np)
                    flagged_neighbors = self.get_flagged_neighbors(row, col, board_np)

                    # Skip if no unrevealed neighbors (nothing to deduce)
                    if not unrevealed_neighbors:
                        continue

                    # Basic Rule 1: If flagged neighbors = cell number, remaining neighbors are safe
                    if len(flagged_neighbors) == cell_number:
                        for neighbor in unrevealed_neighbors:
                            if neighbor not in safe_cells:
                                safe_cells.append(neighbor)
                                print(f"✅ Safe cell found: {neighbor} (all mines flagged around {row},{col})")

                    # Basic Rule 2: If unrevealed neighbors = remaining mines needed, all are mines
                    remaining_mines = cell_number - len(flagged_neighbors)
                    if remaining_mines == len(unrevealed_neighbors) and remaining_mines > 0:
                        for neighbor in unrevealed_neighbors:
                            if neighbor not in mine_cells:
                                mine_cells.append(neighbor)
                                print(f"💣 Mine found: {neighbor} (must be mine around {row},{col})")

        # Remove duplicates
        safe_cells = list(set(safe_cells))
        mine_cells = list(set(mine_cells))

        print(f"🎯 Logic found: {len(safe_cells)} safe cells, {len(mine_cells)} mine cells")
        return safe_cells, mine_cells

    def execute_moves(self, safe_cells, mine_cells):
        """Execute the found safe and mine moves"""
        moves_made = 0

        # First, flag all mine cells
        for mine_row, mine_col in mine_cells:
            if (mine_row, mine_col) not in self.flagged_cells:
                if self.is_cell_actually_unrevealed(mine_row, mine_col):
                    print(f"🚩 Flagging mine at ({mine_row}, {mine_col})")
                    self.click_cell(mine_row, mine_col, right_click=True)
                    moves_made += 1

        # Then, click all safe cells
        for safe_row, safe_col in safe_cells:
            if self.is_cell_actually_unrevealed(safe_row, safe_col):
                print(f"🎯 Clicking safe cell at ({safe_row}, {safe_col})")
                self.click_cell(safe_row, safe_col)
                moves_made += 1

        if moves_made > 0:
            print(f"✅ Made {moves_made} logical moves ({len(mine_cells)} flags, {len(safe_cells)} safe clicks)")
        else:
            print("🤔 No logical moves found")

        return moves_made

    def make_random_move(self):
        """Make a random move when no logical moves are available"""
        print("🎲 No logical moves found, making random move...")

        # Get all unrevealed cells
        unrevealed_cells = []
        for row in range(self.board_height):
            for col in range(self.board_width):
                if (row, col) not in self.revealed_cells and (row, col) not in self.flagged_cells:
                    unrevealed_cells.append((row, col))

        if not unrevealed_cells:
            print("🏁 No unrevealed cells found!")
            return False

        # Pick a random cell
        random_cell = random.choice(unrevealed_cells)
        row, col = random_cell
        print(f"🎯 Random move: clicking ({row}, {col})")
        self.click_cell(row, col)
        return True



























    def display_board_summary(self, board_np):
        """Display clean, minimal board state summary"""
        # Collect all revealed cells with their numbers
        revealed_cells_with_numbers = []
        for row in range(self.board_height):
            for col in range(self.board_width):
                if (row, col) in self.revealed_cells:
                    cell_number = self.get_cell_number_safe(row, col, board_np)
                    if cell_number is None or cell_number == 0:
                        revealed_cells_with_numbers.append((row, col, "BLANK"))
                    else:
                        revealed_cells_with_numbers.append((row, col, cell_number))

        # Get current flags
        current_flags = sorted(list(self.flagged_cells))

        # Display the summary
        print(f"Revealed Cells: {sorted(revealed_cells_with_numbers)}")
        print(f"Current Flags: {current_flags}")

























    def play_game(self):
        """Main game playing loop with game over detection and auto-restart"""
        # Ensure board is set up before playing
        if not self.board_region:
            print("⚠️ Board region not set up! Game over detection requires board setup.")
            return

        max_moves = 300  # Generous limit for Expert Minesweeper (30x16 = 480 cells)
        max_games = 10   # Limit number of consecutive games to prevent infinite loops
        moves = 0
        consecutive_games = 0

        while moves < max_moves and consecutive_games < max_games:
            print(f"\nMove {moves + 1}:")

            # Check game state before making moves
            game_state = self.detect_game_state()

            if game_state == GameState.WON:
                print("GAME WON!")
                break

            elif game_state == GameState.LOST:
                print("GAME LOST! Auto-restart disabled for debugging.")
                break  # Stop the game loop instead of auto-restarting

                # DISABLED: Auto-restart functionality
                # Try to handle the loss popup and restart
                # if self.handle_loss_popup():
                #     consecutive_games += 1
                #     moves = 0  # Reset move counter for new game
                #     time.sleep(1.0)
                #     continue
                # else:
                #     print("Failed to restart game automatically")
                #     break

            # Make moves using clean simple logic
            moves_made = self.make_safe_moves()

            # Show board summary after moves
            if moves_made > 0:
                time.sleep(0.1)  # Brief delay for board to update
                board_np = self.analyze_board_state()
                if board_np is not None:
                    # Sync visual state to capture newly revealed cells
                    self.sync_visual_state(board_np)
                    self.display_board_summary(board_np)

            if moves_made == 0:
                # No logical moves found, make random move
                if not self.make_random_move():
                    break
                else:
                    # Show board summary after random move too
                    time.sleep(0.1)  # Brief pause for board to update
                    board_np = self.analyze_board_state()
                    if board_np is not None:
                        # Sync visual state to capture newly revealed cells
                        self.sync_visual_state(board_np)
                        self.display_board_summary(board_np)

            moves += 1

            # Minimal delay between moves to allow game state to update
            time.sleep(0.05)  # Minimal delay for faster gameplay

            # Check for game over after each move
            post_move_state = self.detect_game_state()

            if post_move_state == GameState.WON:
                print("GAME WON!")
                break

            elif post_move_state == GameState.LOST:
                print("GAME LOST! Auto-restart disabled for debugging.")
                break  # Stop the game loop instead of auto-restarting

                # DISABLED: Auto-restart functionality
                # Try to handle the loss popup and restart
                # if self.handle_loss_popup():
                #     consecutive_games += 1
                #     moves = 0  # Reset move counter for new game
                #     time.sleep(1.0)
                #     continue
                # else:
                #     print("Failed to restart game automatically")
                #     break

        print(f"Game session ended after {consecutive_games + 1} games and {moves} total moves")

    def setup_game(self):
        """Integrated game setup: auto detection + mouse calibration"""
        print("\n🚀 AUTO DETECTION + MOUSE CALIBRATION")
        print("Setting up the game automatically...")
        print()

        # Step 1: Auto detection
        print("🔍 Step 1: Detecting Minesweeper board...")
        if self.find_minesweeper_window():
            print("✅ Board detection successful!")
            print()

            # Step 2: Mouse calibration
            print("🎯 Step 2: Setting up mouse calibration...")
            print("Please position your mouse over the specified cells when prompted.")
            print()

            if self.simple_mouse_calibration():
                print("\n🎉 SETUP COMPLETE!")
                print("✅ Board detected AND mouse calibrated!")
                print("🚀 Ready to play Minesweeper!")
                return True
            else:
                print("❌ Mouse calibration failed")
                print("🔧 Board was detected but mouse positioning needs work")
                return False
        else:
            print("❌ Board detection failed")
            print("🔧 Could not automatically find the Minesweeper board")
            return False

    def run_bot(self):
        """Main bot execution - streamlined for direct play"""
        print("🤖 MINESWEEPER BOT")
        print("=" * 30)

        print("🖥️ Multi-monitor support enabled")
        print("📋 Configured for Expert level (30x16)")
        print("🎯 Auto detection + mouse calibration")

        print("\nMake sure:")
        print("- Windows Minesweeper is open")
        print("- Game is on Expert difficulty (30x16)")
        print("- Game window is fully visible")
        print("- No overlapping windows")

        input("\nPress Enter when ready...")

        # Run integrated setup
        if self.setup_game():
            print("\n🚀 Starting game!")
            self.play_game()
        else:
            print("\n❌ Setup failed. Please check Minesweeper is visible and try again.")


def main():
    print("🎮 Windows Minesweeper Bot")
    print("=" * 30)
    print("🎯 Automatic setup and gameplay")
    print("🖥️ Multi-monitor support")
    print("🎲 Expert level (30x16)")

    bot = MinesweeperBot()
    bot.run_bot()


if __name__ == "__main__":
    main()